services:
  angular:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    container_name: matcha-angular
    ports:
      - '80:4200'
    volumes:
      - ./frontend:/app
      - /app/node_modules
    networks:
      - matcha-network

  express:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    container_name: matcha-express
    environment:
      POSTGRES_HOST: ${POSTGRES_HOST}
      POSTGRES_DB: ${POSTGRES_DB}
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      JWT_SECRET: ${JWT_SECRET}
      JWT_EXPIRATION: ${JWT_EXPIRATION}
      SMTP_EMAIL_HOST: ${SMTP_EMAIL_HOST}
      SMTP_EMAIL_PORT: ${SMTP_EMAIL_PORT}
      SMTP_EMAIL_USER: ${SMTP_EMAIL_USER}
      SMTP_EMAIL_PASSWORD: ${SMTP_EMAIL_PASSWORD}
      USER_MVAN_PEE_PASSWORD: ${USER_MVAN_PEE_PASSWORD}
      USER_RPEREZ_T_PASSWORD: ${USER_RPEREZ_T_PASSWORD}
    ports:
      - '3000:3000'
    volumes:
      - ./backend/src:/app/src
    depends_on:
      - postgres
    networks:
      - matcha-network

  postgres:
    image: postgres:13-alpine
    container_name: matcha-postgres
    environment:
      POSTGRES_HOST: ${POSTGRES_HOST}
      POSTGRES_DB: ${POSTGRES_DB}
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    ports:
      - '5432:5432'
    volumes:
      - ./data/postgres:/var/lib/postgresql/data
    networks:
      - matcha-network

networks:
  matcha-network:
    driver: bridge
