.navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #2c2c2c;
  padding: 1rem 2rem;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.navbar-logo a {
  color: #ff6f61;
  font-size: 1.5rem;
  font-weight: bold;
  text-decoration: none;
}

.navbar-links {
  display: flex;
  align-items: center;
}

.navbar-links a {
  color: #fff;
  margin-left: 1.5rem;
  text-decoration: none;
  transition: color 0.3s ease;
}

.navbar-links a:hover {
  color: #ff6f61;
}

.navbar-links .logout-button {
  background: #301eff;
  color: white;
  border: none;
  border-radius: 25px;
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
  cursor: pointer;
  margin-left: 1.5rem;
  transition: background 0.3s ease;
}

.navbar-links .logout-button:hover {
  background: #9187ff;
}

@media (max-width: 768px) {
  .navbar {
    flex-direction: column;
    padding: 1rem;
  }
  
  .navbar-logo {
    margin-bottom: 1rem;
  }
  
  .navbar-links {
    width: 100%;
    justify-content: space-between;
  }
  
  .navbar-links a {
    margin: 0;
    font-size: 0.9rem;
  }
  
  .navbar-links .logout-button {
    margin: 0;
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
  }
}