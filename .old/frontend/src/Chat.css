.chat-page {
  min-height: 100vh;
  background-color: #1a1a1a;
  color: #fff;
}

.chat-container {
  display: flex;
  max-width: 1200px;
  height: calc(100vh - 70px);
  margin: 0 auto;
  background-color: #2c2c2c;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
}

.chat-sidebar {
  width: 300px;
  background-color: #252525;
  border-right: 1px solid #3a3a3a;
  display: flex;
  flex-direction: column;
}

.chat-sidebar h2 {
  padding: 1.5rem;
  margin: 0;
  color: #ff6f61;
  border-bottom: 1px solid #3a3a3a;
}

.connection-list {
  overflow-y: auto;
  flex-grow: 1;
}

.connection-item {
  display: flex;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #3a3a3a;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.connection-item:hover {
  background-color: #333;
}

.connection-item.active {
  background-color: #301eff;
}

.connection-avatar {
  position: relative;
  width: 50px;
  height: 50px;
  margin-right: 1rem;
}

.connection-avatar img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}

.online-indicator {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 12px;
  height: 12px;
  background-color: #4caf50;
  border-radius: 50%;
  border: 2px solid #252525;
}

.connection-info {
  flex-grow: 1;
}

.connection-info h3 {
  margin: 0 0 0.3rem;
  font-size: 1rem;
}

.last-message {
  margin: 0;
  font-size: 0.8rem;
  color: #aaa;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
}

.chat-main {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #3a3a3a;
}

.chat-user-info h2 {
  margin: 0 0 0.3rem;
  color: #fff;
}

.online-status {
  color: #4caf50;
  font-size: 0.8rem;
}

.offline-status {
  color: #aaa;
  font-size: 0.8rem;
}

.view-profile-button {
  background-color: #301eff;
  color: white;
  border: none;
  border-radius: 20px;
  padding: 0.5rem 1rem;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.view-profile-button:hover {
  background-color: #9187ff;
}

.chat-messages {
  flex-grow: 1;
  padding: 1.5rem;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.message {
  max-width: 70%;
  margin-bottom: 1rem;
  display: flex;
}

.message.sent {
  align-self: flex-end;
}

.message.received {
  align-self: flex-start;
}

.message-content {
  padding: 0.8rem 1rem;
  border-radius: 18px;
  position: relative;
}

.message.sent .message-content {
  background-color: #301eff;
  border-bottom-right-radius: 4px;
}

.message.received .message-content {
  background-color: #444;
  border-bottom-left-radius: 4px;
}

.message-content p {
  margin: 0 0 0.5rem;
}

.message-time {
  font-size: 0.7rem;
  color: rgba(255, 255, 255, 0.7);
  display: block;
  text-align: right;
}

.chat-input {
  display: flex;
  padding: 1rem;
  border-top: 1px solid #3a3a3a;
}

.chat-input input {
  flex-grow: 1;
  padding: 0.8rem;
  border: 1px solid #444;
  border-radius: 25px;
  background-color: #333;
  color: #fff;
  font-size: 1rem;
}

.chat-input button {
  background-color: #301eff;
  color: white;
  border: none;
  border-radius: 25px;
  padding: 0 1.5rem;
  margin-left: 0.5rem;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.chat-input button:hover {
  background-color: #9187ff;
}

.no-chat-selected, .no-connections, .no-messages, .loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #aaa;
  text-align: center;
  padding: 2rem;
}