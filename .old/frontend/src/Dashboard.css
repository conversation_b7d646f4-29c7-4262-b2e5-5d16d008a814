.dashboard-page {
  min-height: 100vh;
  background-color: #1a1a1a;
  color: #fff;
}

.dashboard-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.dashboard-container h1 {
  margin-bottom: 2rem;
  color: #ff6f61;
  text-align: center;
}

.suggestions h2 {
  margin-bottom: 1.5rem;
  color: #fff;
  text-align: center;
}

.suggestion-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 2rem;
}

.suggestion-card {
  background-color: #2c2c2c;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
  transition: transform 0.3s ease;
}

.suggestion-card:hover {
  transform: translateY(-5px);
}

.suggestion-image {
  height: 200px;
  overflow: hidden;
}

.suggestion-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.suggestion-info {
  padding: 1.5rem;
}

.suggestion-info h3 {
  margin: 0 0 0.5rem;
  color: #ff6f61;
}

.location {
  color: #ccc;
  margin-bottom: 0.5rem;
}

.fame {
  color: #ffd700;
  margin-bottom: 1rem;
}

.tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.tag {
  background-color: #301eff;
  color: white;
  padding: 0.3rem 0.6rem;
  border-radius: 15px;
  font-size: 0.8rem;
}

.view-profile-button {
  width: 100%;
  background-color: #301eff;
  color: white;
  border: none;
  padding: 1rem;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.view-profile-button:hover {
  background-color: #9187ff;
}

.loading, .message {
  text-align: center;
  margin: 3rem 0;
  font-size: 1.2rem;
}

.message {
  color: #ff6f61;
}