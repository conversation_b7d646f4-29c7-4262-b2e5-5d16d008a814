/** @type {import('tailwindcss').Config} */
module.exports = {
	content: ['./src/**/*.{html,ts}'],
	theme: {
		extend: {
			fontFamily: {
				sans: ['Inter', 'system-ui', 'sans-serif'],
				serif: ['Roboto Slab', 'serif'],
				mono: ['"IBM Plex Mono"', 'monospace'],
			},
			screens: {
				'6xs': '160px',
				'5xs': '200px',
				'4xs': '240px',
				'3xs': '280px',
				'2xs': '320px',
				xs: '480px',
				sm: '640px',
				md: '768px',
				lg: '1024px',
				xl: '1280px',
				'2xl': '1536px',
				'3xl': '1920px',
			},
			colors: {
				primary: '#f2f2f2', // White
				secondary: '#1a1a1a', // Black
				tertiary: '#DB700D', // Accent Color
				quaternary: '#B30DDB', // Complementary Accent Color
			},
		},
	},
	plugins: [],
};

// primary (couleur dominante)
// secondary (complémentaire)
// accent (CTA/accents)
// neutral (gris/textes)
// success/warning/error (états)
