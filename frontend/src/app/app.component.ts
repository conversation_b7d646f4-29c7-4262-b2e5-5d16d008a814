import { Component, OnInit } from '@angular/core';
import { NavigationEnd, Router, RouterOutlet } from '@angular/router';
import { SeoService } from './services/seo.service';
import { filter } from 'rxjs/operators';
import { AuthService } from './services/auth.service';
import { User } from './models/user.model';

@Component({
	selector: 'app-root',
	imports: [RouterOutlet],
	templateUrl: './app.component.html',
})
export class AppComponent implements OnInit {
	constructor(
		private router: Router,
		private seoService: SeoService,
		private authService: AuthService
	) {}

	ngOnInit(): void {
		this.router.events
			.pipe(filter((event) => event instanceof NavigationEnd))
			.subscribe(() => {
				this.seoService.update();
				window.scrollTo(0, 0);
			});
		this.authService.me().subscribe({
			next: (user: User | null) => {
				this.authService.user.set(user);
			},
		});
	}
}
