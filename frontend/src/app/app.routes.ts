import { Routes } from '@angular/router';
import { MainLayout } from './layouts/main/main.layout';
import { HomePage } from './pages/home/<USER>';
import { LoginPage } from './pages/login/login.page';
import { NotFoundPage } from './pages/not-found/not-found.page';
import { UserAuthGuard } from './guards/user.auth.guard';
import { ProfilePage } from './pages/profile/profile.page';

export const routes: Routes = [
	{
		path: '',
		component: MainLayout,
		children: [
			{
				path: '',
				component: HomePage,
				pathMatch: 'full',
				canActivate: [UserAuthGuard],
			},
			{
				path: 'profile',
				component: ProfilePage,
				pathMatch: 'full',
				canActivate: [UserAuthGuard],
			},
			{
				path: 'login',
				component: LoginPage,
				pathMatch: 'full',
			},
			{
				path: '**',
				component: NotFoundPage,
			},
		],
	},
];
