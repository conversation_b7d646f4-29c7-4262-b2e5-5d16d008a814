import { Routes } from '@angular/router';
import { MainLayout } from './layouts/main/main.layout';
import { HomePage } from './pages/home/<USER>';
import { LoginPage } from './pages/login/login.page';
import { SignupPage } from './pages/signup/signup.page';
import { NotFoundPage } from './pages/not-found/not-found.page';
import { UserAuthGuard } from './guards/user.auth.guard';
import { ProfilePage } from './pages/profile/profile.page';
import { ResetPasswordPage } from './pages/reset-password/reset-password.page';
import { ForgotPasswordPage } from './pages/forgot-password/forgot-password.page';
import { EditProfilePage } from './pages/edit-profile/edit-profile.page';
import { MatchPage } from './pages/match/match.page';

export const routes: Routes = [
	{
		path: '',
		component: MainLayout,
		children: [
			{
				path: '',
				component: HomePage,
				pathMatch: 'full',
				canActivate: [UserAuthGuard],
			},
			{
				path: 'match',
				component: MatchPage,
				pathMatch: 'full',
				canActivate: [UserAuthGuard],
			},
			{
				path: 'profile',
				component: ProfilePage,
				pathMatch: 'full',
				canActivate: [UserAuthGuard],
			},
			{
				path: 'profile/edit',
				component: EditProfilePage,
				pathMatch: 'full',
				canActivate: [UserAuthGuard],
			},
			{
				path: 'login',
				component: LoginPage,
				pathMatch: 'full',
			},
			{
				path: 'signup',
				component: SignupPage,
				pathMatch: 'full',
			},
			{
				path: 'reset-password',
				component: ResetPasswordPage,
				pathMatch: 'full',
			},
			{
				path: 'forgot-password',
				component: ForgotPasswordPage,
				pathMatch: 'full',
			},
			{
				path: '**',
				component: NotFoundPage,
			},
		],
	},
];
