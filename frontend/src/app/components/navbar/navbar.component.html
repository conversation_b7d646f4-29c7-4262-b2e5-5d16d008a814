<nav class="bg-white shadow-lg p-4">
	<div class="container mx-auto flex justify-between items-center">
		<a
			routerLink="/"
			class="text-2xl font-extrabold tracking-wide drop-shadow-lg hover:scale-105 transition-transform duration-200 bg-gradient bg-clip-text text-transparent"
		>
			MATCHA
		</a>

		<div class="hidden md:flex space-x-4 items-center">
			<ng-container *ngIf="user(); else guest">
				<a routerLink="/profile" class="font-semibold">
					{{ user()?.username }}
				</a>
				<button
					(click)="handleLogout()"
					class="px-6 py-2 min-w-[100px] text-center bg-red-500 text-white rounded-full shadow hover:bg-red-600 transition"
				>
					Logout
				</button>
			</ng-container>

			<ng-template #guest>
				<a
					routerLink="/login"
					class="px-6 py-2 min-w-[100px] text-center bg-purple-700 text-white rounded-full shadow hover:bg-purple-800 transition"
				>
					Login
				</a>
				<a
					routerLink="/signup"
					class="px-6 py-2 min-w-[100px] text-center bg-orange-500 text-white rounded-full shadow hover:bg-orange-600 transition"
				>
					Sign Up
				</a>
			</ng-template>
		</div>

		<div class="md:hidden">
			<button (click)="toggleMenu()" class="text-black focus:outline-none">
				<svg
					class="w-6 h-6"
					fill="none"
					stroke="currentColor"
					viewBox="0 0 24 24"
					xmlns="http://www.w3.org/2000/svg"
				>
					<path
						stroke-linecap="round"
						stroke-linejoin="round"
						stroke-width="2"
						[attr.d]="
							isOpen ? 'M6 18L18 6M6 6l12 12' : 'M4 6h16M4 12h16M4 18h16'
						"
					></path>
				</svg>
			</button>
		</div>
	</div>

	<div *ngIf="isOpen" class="md:hidden mt-8 space-y-3">
		<ng-container *ngIf="!user(); else mobileUserLinks">
			<a
				routerLink="/login"
				(click)="isOpen = false"
				class="text-center block px-6 py-2 bg-purple-700 text-white rounded-full shadow hover:bg-purple-800 transition"
			>
				Login
			</a>
			<a
				routerLink="/signup"
				(click)="isOpen = false"
				class="text-center block px-6 py-2 bg-orange-500 text-white rounded-full shadow hover:bg-orange-600 transition"
			>
				Sign Up
			</a>
		</ng-container>

		<ng-template #mobileUserLinks>
			<a
				routerLink="/profile"
				(click)="isOpen = false"
				class="text-center block w-full px-6 py-2 bg-gray-200 text-gray-800 rounded-full shadow hover:bg-gray-300 transition"
			>
				{{ user()?.username }}
			</a>
			<button
				(click)="isOpen = false; handleLogout()"
				class="text-center block w-full px-6 py-2 bg-red-500 text-white rounded-full shadow hover:bg-red-600 transition"
			>
				Logout
			</button>
		</ng-template>
	</div>
</nav>
