import { Component, inject, OnInit, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { RouterLink } from '@angular/router';
import { User } from '../../models/user.model';
import { AuthService } from '../../services/auth.service';

@Component({
	selector: 'component-navbar',
	imports: [CommonModule, RouterModule, RouterLink],
	templateUrl: './navbar.component.html',
})
export class NavbarComponent {
	public user = signal<User | null>(null);

	public isOpen: boolean = false;

	constructor(public authService: AuthService) {
		this.user = this.authService.user;
	}

	public toggleMenu() {
		this.isOpen = !this.isOpen;
	}

	public handleLogout(): void {
		this.authService.logout();
	}
}
