import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { StorageService } from '../../services/storage.service';
import { environment } from '../../../environments/environment';
import { Notification } from '../../models/notification.model';
import { NotificationService } from '../../services/notification.service';

@Component({
	selector: 'component-notification',
	imports: [CommonModule, RouterModule],
	templateUrl: './notification.component.html',
})
export class NotificationComponent implements OnInit {
	constructor(
		private _storageService: StorageService,
		private _notificationService: NotificationService
	) {}

	private _socket: WebSocket | null = null;
	private _pingIntervalId: any;
	public readedNotifications: Notification[] = [];
	public unreadNotifications: Notification[] = [];
	public isOpen: boolean = false;

	public get hasNotifications(): boolean {
		return this.unreadNotifications.length > 0;
	}

	public toggleNotifications(): void {
		this.isOpen = !this.isOpen;
		if (this.isOpen) {
			this._putAllNotificationsAsRead();
		}
		if (!this.isOpen) {
			const readNow = this.unreadNotifications.map((notification) => ({
				...notification,
				is_read: true,
			}));
			this.readedNotifications.unshift(...readNow);
			this.unreadNotifications = [];
		}
	}

	private _putAllNotificationsAsRead(): void {
		this._notificationService.putAllNotificationsAsRead().subscribe({
			next: () => {},
			error: (error) => {
				console.error('Error marking notifications as read:', error);
			},
		});
	}

	private _getNotifications(): void {
		this._notificationService.getNotifications().subscribe({
			next: (notifications) => {
				this.readedNotifications = notifications.filter(
					(notification) => notification.is_read
				);
				this.unreadNotifications = notifications.filter(
					(notification) => !notification.is_read
				);
			},
			error: (error) => {
				console.error('Error fetching read notifications:', error);
			},
		});
	}

	private _disconnectWebSocket(): void {
		if (this._socket) {
			this._socket.close();
			this._socket = null;
		}
	}

	private _connectWebSocket(): void {
		this._disconnectWebSocket();

		this._socket = new WebSocket(
			`${
				environment.websocketUrl
			}/notification?token=${this._storageService.getToken()}`
		);

		this._socket.onopen = () => {
			console.log('Connected to notification WebSocket');
		};

		this._socket.onmessage = (event) => {
			const data = JSON.parse(event.data);
			if (data) {
				this.unreadNotifications.unshift({
					user_id: data.user_id,
					title: data.title,
					content: data.content,
					is_read: data.is_read,
					created_at: new Date(data.created_at),
				});
			}
		};

		this._socket.onclose = () => {
			this._socket = null;
		};

		this._socket.onerror = (error) => {
			console.error('WebSocket error:', error);
			this._socket = null;
		};
	}

	private _startPinging(): void {
		this._pingIntervalId = setInterval(() => {
			if (this._socket && this._socket.readyState === WebSocket.OPEN) {
				this._socket.send(JSON.stringify({ type: 'ping' }));
			}
		}, 30000); // every 30 seconds
	}

	private _stopPinging(): void {
		if (this._pingIntervalId) {
			clearInterval(this._pingIntervalId);
			this._pingIntervalId = null;
		}
	}

	public ngOnInit(): void {
		this._connectWebSocket();
		this._getNotifications();
		this._startPinging();
	}

	public ngOnDestroy(): void {
		this._disconnectWebSocket();
		this._stopPinging();
	}
}
