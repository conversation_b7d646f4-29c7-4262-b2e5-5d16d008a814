<div class="fixed bottom-4 right-4">
	<button
		(click)="toggleNotifications()"
		class="relative bg-gray-200 focus:outline-none p-3 rounded-full shadow-md transition duration-200"
		aria-label="Toggle notifications"
	>
		<!-- Notification Bell Icon -->
		<img src="/icons/bell.svg" alt="bell icon" class="w-6 h-6" />

		<!-- Red Notification Badge -->
		<span
			*ngIf="hasNotifications"
			class="absolute -top-1 -right-1 inline-flex items-center justify-center px-2 py-0.5 text-xs font-bold leading-none text-white bg-red-600 rounded-full"
		>
			{{ unreadNotifications.length > 99 ? '99+' : unreadNotifications.length }}
		</span>
	</button>
</div>

<div *ngIf="isOpen">
	<div
		class="fixed inset-0 bg-gray-900 bg-opacity-50 flex items-center justify-center"
		(mousedown)="toggleNotifications()"
	>
		<div
			class="bg-white rounded-lg shadow-lg p-6 w-96"
			(mousedown)="$event.stopPropagation()"
		>
			<h2 class="text-xl font-semibold mb-4">Notifications</h2>
			<ul class="space-y-4 overflow-y-scroll max-h-96">
				<li
					*ngFor="let notification of unreadNotifications"
					class="p-4 border-b"
				>
					<p class="text-gray-800">{{ notification.title }}</p>
					<p class="text-gray-600">{{ notification.content }}</p>
					<small class="text-gray-500">{{
						notification.created_at | date : 'short'
					}}</small>
				</li>
				<div class="text-center text-gray-800 bg-gray-200 p-2 rounded-xl">
					Already read notifications
				</div>
				<li
					*ngFor="let notification of readedNotifications"
					class="p-4 border-b bg-gray-100"
				>
					<p class="text-gray-800">{{ notification.title }}</p>
					<p class="text-gray-600">{{ notification.content }}</p>
					<small class="text-gray-500">{{
						notification.created_at | date : 'short'
					}}</small>
				</li>
			</ul>
			<button
				(click)="toggleNotifications()"
				class="mt-4 bg-blue-500 text-white px-4 py-2 rounded"
			>
				Close
			</button>
		</div>
	</div>
</div>
