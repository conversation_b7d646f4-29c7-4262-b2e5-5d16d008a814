import { Directive, ElementRef, Input, OnInit, On<PERSON><PERSON>roy } from '@angular/core';

@Directive({
	selector: '[aos]',
})
export class AosDirective implements OnInit, OnDestroy {
	@Input() aos:
		| 'fade-in'
		| 'fade-up'
		| 'fade-down'
		| 'fade-left'
		| 'fade-right'
		| 'zoom-in' = 'fade-in';
	@Input() duration: number = 400;
	@Input() delay: number = 0;

	private observer: IntersectionObserver | undefined;

	constructor(private el: ElementRef) {}

	ngOnInit(): void {
		const element = this.el.nativeElement;

		// Ajouter la transition AOS sans écraser les autres transitions
		element.style.transition = `opacity ${this.duration}ms ease-out ${this.delay}ms, 
       transform ${this.duration}ms ease-out ${this.delay}ms`;

		element.style.opacity = '0';
		this.applyInitialTransform();

		this.observer = new IntersectionObserver(
			(entries) => {
				entries.forEach((entry) => {
					if (entry.isIntersecting) {
						this.animateElement();
						this.observer?.unobserve(element);
					}
				});
			},
			{ threshold: 0.1 }
		);

		this.observer.observe(element);
	}

	private applyInitialTransform(): void {
		const transforms = {
			'fade-up': 'translateY(20px)',
			'fade-down': 'translateY(-20px)',
			'fade-left': 'translateX(20px)',
			'fade-right': 'translateX(-20px)',
			'zoom-in': 'scale(0.9)',
			'fade-in': 'none',
		};
		this.el.nativeElement.style.transform = transforms[this.aos];
	}

	private animateElement(): void {
		this.el.nativeElement.style.opacity = '1';
		this.el.nativeElement.style.transform =
			this.el.nativeElement.style.transform.replace(
				/translate[XY]\([^)]+\)/,
				''
			);
	}

	ngOnDestroy(): void {
		this.observer?.disconnect();
	}
}
