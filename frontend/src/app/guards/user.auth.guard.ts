import { Injectable } from '@angular/core';
import {
	ActivatedRouteSnapshot,
	CanActivate,
	Router,
	RouterStateSnapshot,
} from '@angular/router';
import { AuthService } from '../services/auth.service';
import { Observable, of } from 'rxjs';
import { map, catchError } from 'rxjs/operators';

@Injectable({
	providedIn: 'root',
})
export class UserAuthGuard implements CanActivate {
	constructor(private authService: AuthService, private router: Router) {}

	canActivate(
		next: ActivatedRouteSnapshot,
		state: RouterStateSnapshot
	): Observable<boolean> {
		return this.authService.me().pipe(
			map((user) => {
				if (user) {
					this.authService.user.set(user);
					return true;
				} else {
					this.router.navigate(['/login'], {
						queryParams: { to: state.url },
					});
					return false;
				}
			}),
			catchError(() => {
				this.router.navigate(['/login'], {
					queryParams: { to: state.url },
				});
				return [false];
			})
		);
	}
}
