import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { StorageService } from './storage.service';
import { environment } from '../../environments/environment';
import { Observable } from 'rxjs';
import { User } from '../models/user.model';

@Injectable({
	providedIn: 'root',
})
export class ViewService {
	constructor(private _http: HttpClient, private _storage: StorageService) {}

	public getViews(): Observable<User[]> {
		const headers = this._storage.getHeaders();
		return this._http.get<User[]>(`${environment.apiUrl}/view/`, {
			headers,
		});
	}

	public getViewsById(userId: number): Observable<User[]> {
		const headers = this._storage.getHeaders();
		return this._http.get<User[]>(`${environment.apiUrl}/view/${userId}`, {
			headers,
		});
	}

	public createView(toUserId: number): Observable<{ message: string }> {
		const headers = this._storage.getHeaders();
		return this._http.post<{ message: string }>(
			`${environment.apiUrl}/view/${toUserId}`,
			{},
			{ headers }
		);
	}
}
