import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { StorageService } from './storage.service';
import { Observable } from 'rxjs';
import { environment } from '../../environments/environment';

@Injectable({
	providedIn: 'root',
})
export class BlockService {
	constructor(private _http: HttpClient, private _storage: StorageService) {}

	public toggleBlock(userId: number): Observable<{ message: string }> {
		const headers = this._storage.getHeaders();
		return this._http.post<{ message: string }>(
			`${environment.apiUrl}/block/${userId}`,
			{},
			{
				headers,
			}
		);
	}

	public isBlocked(userId: number): Observable<boolean> {
		const headers = this._storage.getHeaders();
		return this._http.get<boolean>(`${environment.apiUrl}/block/${userId}`, {
			headers,
		});
	}
}
