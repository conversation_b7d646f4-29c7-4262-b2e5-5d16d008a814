import { Injectable, Inject } from '@angular/core';
import { DOCUMENT } from '@angular/common';
import { Router } from '@angular/router';

@Injectable({
	providedIn: 'root',
})
export class SeoService {
	constructor(
		@Inject(DOCUMENT) private dom: Document,
		private router: Router
	) {}

	private get url(): string {
		return this.dom.location.origin + this.router.url;
	}

	update(): void {
		const canonicalUrl = this.url;
		let link = this.dom.querySelector(
			`link[rel='canonical']`
		) as HTMLLinkElement;

		if (link) {
			link.href = canonicalUrl;
		} else {
			link = this.dom.createElement('link') as HTMLLinkElement;
			link.rel = 'canonical';
			link.href = canonicalUrl;
			this.dom.head.appendChild(link);
		}
	}
}
