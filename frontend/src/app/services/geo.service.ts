import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';

@Injectable({
	providedIn: 'root',
})
export class GeoService {
	constructor(private http: HttpClient) {}

	public getCurrentPosition(): Promise<GeolocationPosition> {
		return new Promise((resolve, reject) => {
			if (!navigator.geolocation) {
				reject(new Error('Geolocation is not supported by this browser.'));
			}
			navigator.geolocation.getCurrentPosition(resolve, reject);
		});
	}

	public async reverseGeocode(
		lat: number,
		lon: number
	): Promise<{ city?: string; country?: string }> {
		const url = `https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lon}`;
		try {
			const response = await this.http.get<any>(url).toPromise();
			if (response && response.address) {
				return {
					city:
						response.address.city ||
						response.address.town ||
						response.address.village,
					country: response.address.country,
				};
			} else {
				throw new Error('No address found for the provided coordinates.');
			}
		} catch (error) {
			console.error('Error fetching address:', error);
			throw error;
		}
	}
}
