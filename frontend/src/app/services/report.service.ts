import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { StorageService } from './storage.service';
import { Observable } from 'rxjs';
import { environment } from '../../environments/environment';

@Injectable({
	providedIn: 'root',
})
export class ReportService {
	constructor(private _http: HttpClient, private _storage: StorageService) {}

	public toggleReport(userId: number): Observable<{ message: string }> {
		const headers = this._storage.getHeaders();
		return this._http.post<{ message: string }>(
			`${environment.apiUrl}/report/${userId}`,
			{},
			{
				headers,
			}
		);
	}

	public isReported(userId: number): Observable<boolean> {
		const headers = this._storage.getHeaders();
		return this._http.get<boolean>(`${environment.apiUrl}/report/${userId}`, {
			headers,
		});
	}
}
