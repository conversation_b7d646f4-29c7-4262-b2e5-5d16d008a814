import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { StorageService } from './storage.service';
import { environment } from '../../environments/environment';
import { Observable } from 'rxjs';
import { User } from '../models/user.model';

@Injectable({
	providedIn: 'root',
})
export class LikeService {
	constructor(private _http: HttpClient, private _storage: StorageService) {}

	public getLikesById(userId: number): Observable<User[]> {
		const headers = this._storage.getHeaders();
		return this._http.get<User[]>(`${environment.apiUrl}/like/${userId}`, {
			headers,
		});
	}

	public isLiked(userId: number): Observable<{ liked: boolean }> {
		const headers = this._storage.getHeaders();
		return this._http.get<{ liked: boolean }>(
			`${environment.apiUrl}/like/is-liked/${userId}`,
			{
				headers,
			}
		);
	}

	public toggleLike(userId: number): Observable<{ message: string }> {
		const headers = this._storage.getHeaders();
		return this._http.post<{ message: string }>(
			`${environment.apiUrl}/like/${userId}`,
			{},
			{
				headers,
			}
		);
	}

	public getMatchs(): Observable<User[]> {
		const headers = this._storage.getHeaders();
		return this._http.get<User[]>(`${environment.apiUrl}/like/matchs`, {
			headers,
		});
	}

	public isMutualLike(userId: number): Observable<{ mutual: boolean }> {
		const headers = this._storage.getHeaders();
		return this._http.get<{ mutual: boolean }>(
			`${environment.apiUrl}/like/mutual/${userId}`,
			{
				headers,
			}
		);
	}

	public getFollowing(userId: number): Observable<User[]> {
		const headers = this._storage.getHeaders();
		return this._http.get<User[]>(`${environment.apiUrl}/like/following/${userId}`, {
			headers,
		});
	}
}
