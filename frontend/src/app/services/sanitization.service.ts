import { Injectable } from '@angular/core';

@Injectable({
	providedIn: 'root',
})
export class SanitizationService {
	constructor() {}

	/**
	 * Sanitizes text content to prevent XSS attacks
	 * @param content The content to sanitize
	 * @returns Sanitized content safe for display
	 */
	public sanitizeText(content: string): string {
		if (!content) return '';

		// Remove HTML tags and decode HTML entities
		const div = document.createElement('div');
		div.textContent = content;
		let sanitized = div.innerHTML;

		// Additional sanitization - remove any remaining script-like content
		sanitized = sanitized
			.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
			.replace(/javascript:/gi, '')
			.replace(/on\w+\s*=/gi, '')
			.replace(/data:/gi, '');

		return sanitized;
	}

	/**
	 * Validates message content
	 * @param content The message content to validate
	 * @returns Validation result with isValid flag and error message
	 */
	public validateMessage(content: string): { isValid: boolean; error?: string } {
		if (!content || content.trim().length === 0) {
			return { isValid: false, error: 'Message cannot be empty' };
		}

		if (content.length > 100) {
			return { isValid: false, error: 'Message must be 100 characters or less' };
		}

		// Check for suspicious patterns
		const suspiciousPatterns = [
			/<script/i,
			/javascript:/i,
			/on\w+\s*=/i,
			/data:text\/html/i,
			/vbscript:/i,
		];

		for (const pattern of suspiciousPatterns) {
			if (pattern.test(content)) {
				return { isValid: false, error: 'Message contains invalid content' };
			}
		}

		return { isValid: true };
	}

	/**
	 * Sanitizes and validates message content
	 * @param content The message content
	 * @returns Object with sanitized content and validation result
	 */
	public processMessage(content: string): {
		sanitized: string;
		isValid: boolean;
		error?: string;
	} {
		const validation = this.validateMessage(content);
		if (!validation.isValid) {
			return {
				sanitized: '',
				isValid: false,
				error: validation.error,
			};
		}

		const sanitized = this.sanitizeText(content);
		return {
			sanitized,
			isValid: true,
		};
	}

	/**
	 * Escapes HTML characters for safe display
	 * @param text The text to escape
	 * @returns HTML-escaped text
	 */
	public escapeHtml(text: string): string {
		const div = document.createElement('div');
		div.textContent = text;
		return div.innerHTML;
	}
}
