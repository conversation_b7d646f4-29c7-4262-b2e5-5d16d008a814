import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { StorageService } from './storage.service';
import { environment } from '../../environments/environment';
import { Observable } from 'rxjs';
import { Message } from '../models/message.model';

@Injectable({
	providedIn: 'root',
})
export class MessageService {
	constructor(private _http: HttpClient, private _storage: StorageService) {}

	public getMessages(recipientUserId: number): Observable<Message[]> {
		const headers = this._storage.getHeaders();
		return this._http.get<Message[]>(
			`${environment.apiUrl}/message/${recipientUserId}`,
			{
				headers,
			}
		);
	}
}
