import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { StorageService } from './storage.service';
import { environment } from '../../environments/environment';
import { Observable } from 'rxjs';
import { User } from '../models/user.model';

@Injectable({
	providedIn: 'root',
})
export class UsersService {
	constructor(private _http: HttpClient, private _storage: StorageService) {}

	public profile(id: number): Observable<User | null> {
		const headers = this._storage.getHeaders();
		return this._http.get<User | null>(`${environment.apiUrl}/users/${id}`, {
			headers,
		});
	}
}
