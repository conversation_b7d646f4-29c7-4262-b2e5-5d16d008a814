import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { StorageService } from './storage.service';
import { environment } from '../../environments/environment';
import { Observable } from 'rxjs';
import { User } from '../models/user.model';

@Injectable({
	providedIn: 'root',
})
export class UsersService {
	constructor(private _http: HttpClient, private _storage: StorageService) {}

	public profile(id: number): Observable<User | null> {
		const headers = this._storage.getHeaders();
		return this._http.get<User | null>(`${environment.apiUrl}/users/${id}`, {
			headers,
		});
	}

	public completeProfile(data: FormData): Observable<any> {
		const headers = this._storage.getHeaders();
		return this._http.post(
			`${environment.apiUrl}/users/complete-profile`,
			data,
			{
				headers,
			}
		);
	}

	getSuggestions(
		options: {
			maxDistanceKm?: number;
			limit?: number;
			offset?: number;
			ageGap?: number;
		} = {}
	): Observable<User[]> {
		const headers = this._storage.getHeaders();

		let params = new HttpParams();
		if (options.maxDistanceKm) {
			params = params.set('maxDistanceKm', options.maxDistanceKm.toString());
		}
		if (options.limit) {
			params = params.set('limit', options.limit.toString());
		}
		if (options.offset) {
			params = params.set('offset', options.offset.toString());
		}

		if (options.ageGap) {
			params = params.set('ageGap', options.ageGap.toString());
		}

		return this._http.get<User[]>(`${environment.apiUrl}/users/suggestions`, {
			params,
			headers,
		});
	}

	public getUserProfilePicture(id: number): Observable<Blob> {
		const headers = this._storage.getHeaders();
		return this._http.get(`${environment.apiUrl}/users/${id}/profile-picture`, {
			headers,
			responseType: 'blob',
		});
	}

	public getUserPictureIds(id: number): Observable<{ pictureIds: number[] }> {
		const headers = this._storage.getHeaders();
		return this._http.get<{ pictureIds: number[] }>(
			`${environment.apiUrl}/users/${id}/picture-ids`,
			{ headers }
		);
	}
}
