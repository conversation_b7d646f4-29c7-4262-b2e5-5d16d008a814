import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { StorageService } from './storage.service';
import { environment } from '../../environments/environment';
import { Observable } from 'rxjs';
import { User } from '../models/user.model';

@Injectable({
	providedIn: 'root',
})
export class UsersService {
	constructor(private _http: HttpClient, private _storage: StorageService) {}

	public profile(id: number): Observable<User | null> {
		const headers = this._storage.getHeaders();
		return this._http.get<User | null>(`${environment.apiUrl}/users/${id}`, {
			headers,
		});
	}

	public completeProfile(data: FormData): Observable<any> {
		const headers = this._storage.getHeaders();
		return this._http.post(
			`${environment.apiUrl}/users/complete-profile`,
			data,
			{
				headers,
			}
		);
	}

	public getSuggestions(
		options: {
			maxDistanceKm?: number;
			limit?: number;
			offset?: number;
			ageGap?: number;
		} = {}
	): Observable<User[]> {
		const headers = this._storage.getHeaders();

		let params = new HttpParams();
		if (options.maxDistanceKm) {
			params = params.set('maxDistanceKm', options.maxDistanceKm.toString());
		}
		if (options.limit) {
			params = params.set('limit', options.limit.toString());
		}
		if (options.offset) {
			params = params.set('offset', options.offset.toString());
		}

		if (options.ageGap) {
			params = params.set('ageGap', options.ageGap.toString());
		}

		return this._http.get<User[]>(`${environment.apiUrl}/users/suggestions`, {
			params,
			headers,
		});
	}

	public getUserProfilePicture(id: number): Observable<Blob> {
		const headers = this._storage.getHeaders();
		return this._http.get(`${environment.apiUrl}/users/${id}/profile-picture`, {
			headers,
			responseType: 'blob',
		});
	}

	public getUserPictureIds(id: number): Observable<{ pictureIds: number[] }> {
		const headers = this._storage.getHeaders();
		return this._http.get<{ pictureIds: number[] }>(
			`${environment.apiUrl}/users/${id}/picture-ids`,
			{ headers }
		);
	}

	public updateBasicInfo(data: {
		first_name?: string;
		last_name?: string;
		email?: string;
	}): Observable<any> {
		const headers = this._storage.getHeaders();
		return this._http.put(
			`${environment.apiUrl}/users/update-basic-info`,
			data,
			{ headers }
		);
	}

	public updateProfileInfo(data: {
		gender?: string;
		sexual_preference?: string[];
		biography?: string;
		interests?: string[];
	}): Observable<any> {
		const headers = this._storage.getHeaders();
		return this._http.put(
			`${environment.apiUrl}/users/update-profile-info`,
			data,
			{ headers }
		);
	}

	public updateLocation(data: {
		city?: string;
		country?: string;
		lat?: number;
		lon?: number;
	}): Observable<any> {
		const headers = this._storage.getHeaders();
		return this._http.put(`${environment.apiUrl}/users/update-location`, data, {
			headers,
		});
	}

	public updatePassword(newPassword: string): Observable<any> {
		const headers = this._storage.getHeaders();
		return this._http.put(
			`${environment.apiUrl}/users/update-password`,
			{ newPassword },
			{
				headers,
			}
		);
	}

	public uploadPicture(pictureFile: File): Observable<any> {
		const headers = this._storage.getHeaders();
		const formData = new FormData();
		formData.append('picture', pictureFile);

		return this._http.post(
			`${environment.apiUrl}/users/upload-picture`,
			formData,
			{ headers }
		);
	}

	public deletePicture(pictureId: number): Observable<any> {
		const headers = this._storage.getHeaders();
		return this._http.delete(
			`${environment.apiUrl}/users/pictures/${pictureId}`,
			{ headers }
		);
	}

	public setProfilePicture(pictureId: number): Observable<any> {
		const headers = this._storage.getHeaders();
		return this._http.put(
			`${environment.apiUrl}/users/pictures/${pictureId}/set-profile`,
			{},
			{ headers }
		);
	}

	public getUserPictures(userId: number): Observable<{ pictureIds: number[] }> {
		const headers = this._storage.getHeaders();
		return this._http.get<{ pictureIds: number[] }>(
			`${environment.apiUrl}/users/${userId}/picture-ids`,
			{ headers }
		);
	}

	public getLastConnection(id: number): Observable<number> {
		const headers = this._storage.getHeaders();
		return this._http.get<number>(
			`${environment.apiUrl}/users/last-connection/${id}`,
			{ headers }
		);
	}
}
