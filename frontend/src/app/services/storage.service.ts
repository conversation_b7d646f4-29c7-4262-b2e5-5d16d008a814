import { HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';

@Injectable({
	providedIn: 'root',
})
export class StorageService {
	private _storage: Storage;

	constructor() {
		this._storage = localStorage;
	}

	setItem(key: string, value: string): void {
		this._storage.setItem(key, value);
	}

	getItem(key: string): string | null {
		return this._storage.getItem(key);
	}

	getToken(): string | null {
		return this.getItem('token');
	}

	getHeaders(): HttpHeaders {
		const token = this.getItem('token');
		let headers = new HttpHeaders();
		if (token) {
			headers = headers.set('Authorization', `Bearer ${token}`);
		}
		return headers;
	}

	removeItem(key: string): void {
		this._storage.removeItem(key);
	}

	clear(): void {
		this._storage.clear();
	}
}
