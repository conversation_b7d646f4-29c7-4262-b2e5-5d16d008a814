import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { StorageService } from './storage.service';
import { environment } from '../../environments/environment';
import { Observable } from 'rxjs';
import { Notification } from '../models/notification.model';

@Injectable({
	providedIn: 'root',
})
export class NotificationService {
	constructor(private _http: HttpClient, private _storage: StorageService) {}

	public getUnreadNotifications(): Observable<Notification[]> {
		const headers = this._storage.getHeaders();
		return this._http.get<Notification[]>(
			`${environment.apiUrl}/notification/unread`,
			{ headers }
		);
	}

	public getNotifications(): Observable<Notification[]> {
		const headers = this._storage.getHeaders();
		return this._http.get<Notification[]>(
			`${environment.apiUrl}/notification`,
			{ headers }
		);
	}

	public putAllNotificationsAsRead(): Observable<void> {
		const headers = this._storage.getHeaders();
		return this._http.put<void>(
			`${environment.apiUrl}/notification/read`,
			{},
			{ headers }
		);
	}
}
