import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable, signal } from '@angular/core';
import { Router } from '@angular/router';
import { StorageService } from './storage.service';
import { environment } from '../../environments/environment';
import { BehaviorSubject, Observable, tap } from 'rxjs';
import { User } from '../models/user.model';

@Injectable({
	providedIn: 'root',
})
export class AuthService {
	constructor(
		private _http: HttpClient,
		private _router: Router,
		private _storage: StorageService
	) {}

	public user = signal<User | null>(null);

	public login(credentials: {
		username: string;
		password: string;
	}): Observable<any> {
		return this._http
			.post<any>(`${environment.apiUrl}/auth/login`, credentials)
			.pipe(
				tap((response) => {
					this._storage.setItem('token', response.token);
					this.me().subscribe((user: User | null) => {
						this.user.set(user);
					});
				})
			);
	}

	public register(user: {
		username: string;
		email: string;
		password: string;
	}): Observable<any> {
		return this._http
			.post<any>(`${environment.apiUrl}/auth/register`, user)
			.pipe(
				tap((response) => {
					this._storage.setItem('token', response.token);
					this.me().subscribe((user) => {
						this.user.set(user);
					});
				})
			);
	}

	public logout(): void {
		this._storage.removeItem('token');
		this.user.set(null);
		this._router.navigate(['/login']);
	}

	public me(): Observable<User | null> {
		const token = this._storage.getItem('token');
		if (!token) {
			this.user.set(null);
			return new BehaviorSubject<User | null>(null).asObservable();
		}
		const headers = this._storage.getHeaders();
		return this._http.get<User>(`${environment.apiUrl}/auth/me`, { headers });
	}
}
