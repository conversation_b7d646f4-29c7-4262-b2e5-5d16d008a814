<div class="max-w-6xl mx-auto p-6 sm:p-10">
	<h2 class="text-3xl font-bold mb-6 text-center">Suggested Matches</h2>

	<!-- Filter UI -->
	<div
		class="flex flex-col sm:flex-row gap-4 items-center justify-between mb-1 p-4 bg-white rounded-xl shadow"
	>
		<div
			class="flex flex-col sm:flex-row items-center gap-4 w-full sm:w-auto mx-auto"
		>
			<label class="text-sm font-medium text-gray-700">
				Max Distance: {{ maxDistanceKm }} km
			</label>
			<input
				type="range"
				min="1"
				max="200"
				step="1"
				class="w-full sm:w-64"
				[(ngModel)]="maxDistanceKm"
				(change)="fetchSuggestions(true)"
			/>
		</div>
	</div>
	<div
		class="flex flex-col sm:flex-row gap-4 items-center justify-between mb-8 p-4 bg-white rounded-xl shadow"
	>
		<div
			class="flex flex-col sm:flex-row items-center gap-4 w-full sm:w-auto mx-auto"
		>
			<label for="ageGap" class="block text-sm font-medium text-gray-700"
				>Age Gap: {{ ageGap }}</label
			>
			<input
				type="range"
				min="1"
				max="50"
				step="1"
				class="w-full sm:w-64"
				[(ngModel)]="ageGap"
				(change)="fetchSuggestions(true)"
			/>
		</div>
	</div>

	<!-- No results -->
	<div *ngIf="suggestions.length === 0" class="text-center text-gray-500">
		No suggestions available.
	</div>

	<!-- Suggestions Grid -->
	<div
		*ngIf="suggestions.length > 0"
		class="grid gap-6 sm:grid-cols-2 lg:grid-cols-3"
	>
		<div
			*ngFor="let user of suggestions"
			class="p-4 bg-white rounded-2xl shadow hover:shadow-md transition-shadow duration-200"
		>
			<img
				[src]="getProfilePictureUrl(user.id)"
				alt="Profile Picture"
				class="w-full h-48 object-cover rounded-xl mb-4"
				onerror="this.onerror=null; this.src='/images/default-profile.png';"
			/>

			<h3 class="text-xl font-semibold mb-1">
				<a
					[routerLink]="['/profile']"
					[queryParams]="{ id: user.id }"
					class="text-purple-700 hover:text-orange-500 transition duration-200"
				>
					{{ user.first_name }} {{ user.last_name }} ({{ user.birthdate | age
					}})
				</a>
			</h3>
			<p class="text-gray-600">{{ user.city }}, {{ user.country }}</p>

			<div class="mt-2 text-sm text-gray-700">
				<span class="font-medium">Interests:</span>
				<span *ngIf="user.interests?.length; else noInterests">
					{{ user.interests!.join(', ') }}
				</span>
				<ng-template #noInterests>No interests listed</ng-template>
			</div>
		</div>
	</div>

	<!-- Load more button -->
	<div *ngIf="hasMore" class="text-center mt-6">
		<button
			(click)="fetchSuggestions(false)"
			class="px-6 py-2 rounded-lg bg-purple-600 text-white hover:bg-purple-700 transition"
		>
			Load More
		</button>
	</div>
</div>
