import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { UsersService } from '../../services/users.service';
import { User } from '../../models/user.model';
import { RouterLink } from '@angular/router';
import { environment } from '../../../environments/environment';
import { FormsModule } from '@angular/forms';
import { AgePipe } from '../../pipes/age.pipe';

@Component({
	selector: 'page-home',
	templateUrl: './home.page.html',
	standalone: true,
	imports: [CommonModule, RouterLink, FormsModule, AgePipe],
})
export class HomePage {
	constructor(private _userService: UsersService) {}

	public suggestions: User[] = [];
	public ageGap = 5;
	public limit = 12;
	public maxDistanceKm = 30;
	public offset = 0;
	public hasMore = true;

	public getProfilePictureUrl(userId: number): string {
		return `${environment.apiUrl}/users/${userId}/profile-picture`;
	}

	public ngOnInit() {
		this.fetchSuggestions(true);
	}

	public fetchSuggestions(reset = false) {
		if (reset) {
			this.offset = 0;
			this.suggestions = [];
		}

		this._userService
			.getSuggestions({
				maxDistanceKm: this.maxDistanceKm,
				limit: this.limit,
				offset: this.offset,
				ageGap: this.ageGap,
			})
			.subscribe({
				next: (users) => {
					this.hasMore = users.length === this.limit;
					this.suggestions = reset ? users : [...this.suggestions, ...users];
					this.offset += this.limit;
				},
				error: (err) => {
					console.error('Error fetching suggestions:', err);
				},
			});
	}
}
