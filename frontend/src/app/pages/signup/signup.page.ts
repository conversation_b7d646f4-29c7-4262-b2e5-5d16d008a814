import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { AuthService } from '../../services/auth.service';
import { Router, RouterLink } from '@angular/router';

@Component({
	selector: 'page-signup',
	templateUrl: './signup.page.html',
	standalone: true,
	imports: [CommonModule, FormsModule, RouterLink],
})
export class SignupPage {
	public signupRequest = {
		first_name: '',
		last_name: '',
		username: '',
		email: '',
		password: '',
		confirmPassword: '',
	};

	public errorMessage?: string = undefined;
	public successMessage?: string = undefined;

	constructor(private authService: AuthService, private router: Router) {}

	public handleSignup(event: Event): void {
		event.preventDefault();

		// Reset messages
		this.errorMessage = undefined;
		this.successMessage = undefined;

		// Validate password confirmation
		if (this.signupRequest.password !== this.signupRequest.confirmPassword) {
			this.errorMessage = 'Passwords do not match';
			return;
		}

		// Prepare data for backend (exclude confirmPassword)
		const { confirmPassword, ...userData } = this.signupRequest;

		this.authService.register(userData).subscribe({
			next: () => {
				this.successMessage =
					'Registration successful! Please verify your email before logging in.';
				// Clear form
				this.signupRequest = {
					first_name: '',
					last_name: '',
					username: '',
					email: '',
					password: '',
					confirmPassword: '',
				};
			},
			error: (error) => {
				this.errorMessage =
					error.error?.error || 'Registration failed. Please try again.';
				// Clear passwords on error
				this.signupRequest.password = '';
				this.signupRequest.confirmPassword = '';
				setTimeout(() => {
					this.errorMessage = undefined;
				}, 4000);
			},
		});
	}

	public isFormValid(): boolean {
		return !!(
			this.signupRequest.first_name &&
			this.signupRequest.last_name &&
			this.signupRequest.username &&
			this.signupRequest.email &&
			this.signupRequest.password &&
			this.signupRequest.confirmPassword
		);
	}
}
