<div class="min-h-[calc(100vh-256px)] flex items-center justify-center px-4">
	<form
		(submit)="handleSignup($event)"
		class="w-full max-w-md bg-white rounded-2xl shadow-lg p-8 border border-purple-200"
	>
		<h2
			class="text-3xl font-extrabold text-purple-700 mb-6 text-center drop-shadow"
		>
			Join <PERSON>
		</h2>

		<p *ngIf="errorMessage" class="text-red-500 text-sm mb-4 text-center">
			{{ errorMessage }}
		</p>

		<p *ngIf="successMessage" class="text-green-500 text-sm mb-4 text-center">
			{{ successMessage }}
		</p>

		<div class="grid grid-cols-2 gap-4 mb-4">
			<div>
				<label class="block text-sm font-medium text-gray-700 mb-1">
					First Name
				</label>
				<input
					type="text"
					class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
					[(ngModel)]="signupRequest.first_name"
					name="first_name"
					autocomplete="given-name"
					required
				/>
			</div>
			<div>
				<label class="block text-sm font-medium text-gray-700 mb-1">
					Last Name
				</label>
				<input
					type="text"
					class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
					[(ngModel)]="signupRequest.last_name"
					name="last_name"
					autocomplete="family-name"
					required
				/>
			</div>
		</div>

		<div class="mb-4">
			<label class="block text-sm font-medium text-gray-700 mb-1">
				Username
			</label>
			<input
				type="text"
				class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
				[(ngModel)]="signupRequest.username"
				name="username"
				autocomplete="username"
				required
			/>
		</div>

		<div class="mb-4">
			<label class="block text-sm font-medium text-gray-700 mb-1">
				Email Address
			</label>
			<input
				type="email"
				class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
				[(ngModel)]="signupRequest.email"
				name="email"
				autocomplete="email"
				required
			/>
		</div>

		<div class="mb-4">
			<label class="block text-sm font-medium text-gray-700 mb-1">
				Password
			</label>
			<input
				type="password"
				class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-400"
				[(ngModel)]="signupRequest.password"
				name="password"
				autocomplete="new-password"
				required
			/>
			<p class="text-xs text-gray-500 mt-1">
				Must be at least 8 characters with uppercase, lowercase, numbers, and
				special characters
			</p>
		</div>

		<div class="mb-6">
			<label class="block text-sm font-medium text-gray-700 mb-1">
				Confirm Password
			</label>
			<input
				type="password"
				class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-400"
				[(ngModel)]="signupRequest.confirmPassword"
				name="confirmPassword"
				autocomplete="new-password"
				required
			/>
		</div>

		<button
			type="submit"
			class="w-full py-3 rounded-lg bg-gradient text-white font-semibold shadow hover:from-purple-700 hover:to-orange-600 transition-all duration-200 disabled:cursor-not-allowed disabled:opacity-50"
			[disabled]="!isFormValid()"
		>
			Create Account
		</button>

		<div class="mt-4 text-center">
			<p class="text-sm text-gray-600">
				Already have an account?
				<a
					routerLink="/login"
					class="text-purple-600 hover:text-purple-800 font-medium"
				>
					Sign in here
				</a>
			</p>
		</div>
	</form>
</div>
