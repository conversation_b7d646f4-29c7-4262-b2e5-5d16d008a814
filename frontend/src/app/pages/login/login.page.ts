import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { AuthService } from '../../services/auth.service';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';

@Component({
	selector: 'page-login',
	templateUrl: './login.page.html',
	standalone: true,
	imports: [CommonModule, FormsModule, RouterLink],
})
export class LoginPage {
	public loginRequest = {
		username: '',
		password: '',
	};

	public errorMessage?: string = undefined;
	private returnUrl: string = '/';

	constructor(
		private authService: AuthService,
		private route: Router,
		private activatedRoute: ActivatedRoute
	) {}

	public handleLogin(event: Event): void {
		event.preventDefault();
		this.authService.login(this.loginRequest).subscribe({
			next: () => {
				this.route.navigateByUrl(this.returnUrl);
			},
			error: (error) => {
				this.errorMessage =
					error.error?.error || 'Login failed. Please try again.';
				this.loginRequest.password = '';
				setTimeout(() => {
					this.errorMessage = undefined;
				}, 4000);
			},
		});
	}

	public ngOnInit() {
		this.returnUrl =
			this.activatedRoute.snapshot.queryParams['to'] || '/profile';
	}
}
