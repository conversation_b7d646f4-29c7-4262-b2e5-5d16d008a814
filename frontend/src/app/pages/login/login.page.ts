import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { AuthService } from '../../services/auth.service';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { GeoService } from '../../services/geo.service';

@Component({
	selector: 'page-login',
	templateUrl: './login.page.html',
	standalone: true,
	imports: [CommonModule, FormsModule, RouterLink],
})
export class LoginPage {
	public loginRequest = {
		username: '',
		password: '',
		lat: 0,
		lon: 0,
		city: '',
		country: '',
	};

	public errorMessage?: string = undefined;
	private returnUrl: string = '/';
	private location: GeolocationPosition | undefined = undefined;
	private address: { city?: string; country?: string } = {};

	constructor(
		private authService: AuthService,
		private geoService: GeoService,
		private route: Router,
		private activatedRoute: ActivatedRoute
	) {}

	public async getLocation() {
		try {
			this.location = await this.geoService.getCurrentPosition();
			this.address = await this.geoService.reverseGeocode(
				this.location.coords.latitude,
				this.location.coords.longitude
			);
			this.loginRequest.lat = this.location.coords.latitude;
			this.loginRequest.lon = this.location.coords.longitude;
			this.loginRequest.city = this.address.city ?? '';
			this.loginRequest.country = this.address.country ?? '';
		} catch (error) {
			console.error('Error getting location:', error);
		}
	}

	public handleLogin(event: Event): void {
		event.preventDefault();

		this.authService.login(this.loginRequest).subscribe({
			next: () => {
				this.route.navigateByUrl(this.returnUrl);
			},
			error: (error) => {
				this.errorMessage =
					error.error?.error || 'Login failed. Please try again.';
				this.loginRequest.password = '';
				setTimeout(() => {
					this.errorMessage = undefined;
				}, 4000);
			},
		});
	}

	public ngOnInit() {
		this.getLocation();
		this.returnUrl =
			this.activatedRoute.snapshot.queryParams['to'] || '/profile';
	}
}
