import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { AuthService } from '../../services/auth.service';
import { ActivatedRoute, Router } from '@angular/router';

@Component({
	selector: 'page-reset-password',
	templateUrl: './reset-password.page.html',
	standalone: true,
	imports: [CommonModule, FormsModule],
})
export class ResetPasswordPage {
	constructor(
		private _authService: AuthService,
		private route: ActivatedRoute,
		private router: Router
	) {}

	public token: string | null = null;
	public newPassword: string = '';
	public confirmPassword: string = '';

	public errorMessage: string | null = null;
	public successMessage: string | null = null;

	public resetPassword(e: Event) {
		e.preventDefault();

		if (this.newPassword !== this.confirmPassword) {
			this.errorMessage = 'Passwords do not match';
			return;
		}

		this.successMessage = null;
		this.errorMessage = null;
		this._authService.resetPassword(this.token!, this.newPassword).subscribe({
			next: (response) => {
				this.successMessage = response.message || 'Password reset successful';
			},
			error: (error) => {
				this.errorMessage = error.error?.error || 'Failed to reset password';
			},
		});
	}

	public ngOnInit() {
		this.route.queryParams.subscribe((params) => {
			const token = params['token'];
			if (!token) {
				this.router.navigate(['/login'], {
					queryParams: { error: 'Invalid token' },
				});
			}
			this.token = token;
		});
	}
}
