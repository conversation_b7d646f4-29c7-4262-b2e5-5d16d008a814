<div class="min-h-[calc(100vh-256px)] px-4">
	<h1 class="text-2xl font-bold text-center my-4">Your Matches</h1>
	<div class="flex flex-col md:flex-row flex-wrap justify-center items-start">
		<div
			*ngFor="let match of matchs"
			class="w-full max-w-md md:w-1/2 lg:w-1/3 px-4 mb-6"
		>
			<div class="bg-white rounded-2xl shadow-lg p-6 border border-purple-200">
				<div class="text-center mb-4">
					<img
						[src]="getProfilePictureUrl(match.id)"
						alt="Profile Picture"
						class="w-full h-48 object-cover rounded-xl mb-4"
						onerror="this.onerror=null; this.src='/images/default-profile.png';"
					/>
				</div>
				<div class="text-center mb-2">
					<a
						[routerLink]="['/profile']"
						[queryParams]="{ id: match.id }"
						class="text-lg font-semibold text-gray-800"
					>
						{{ match.first_name }} {{ match.last_name }}
					</a>
				</div>
				<div class="text-center">
					<a
						[routerLink]="['/chat', match.id]"
						class="inline-block bg-purple-500 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded"
						>Matcha Chat</a
					>
				</div>
			</div>
		</div>
		<div *ngIf="matchs.length === 0" class="w-full text-center text-gray-500">
			<p>You don't have any matches yet.</p>
		</div>
	</div>
</div>
