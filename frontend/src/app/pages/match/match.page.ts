import { Component, OnInit } from '@angular/core';
import { RouterLink } from '@angular/router';
import { LikeService } from '../../services/like.service';
import { User } from '../../models/user.model';
import { CommonModule } from '@angular/common';
import { environment } from '../../../environments/environment';

@Component({
	selector: 'page-match',
	templateUrl: './match.page.html',
	standalone: true,
	imports: [CommonModule, RouterLink],
})
export class MatchPage implements OnInit {
	constructor(private _likeService: LikeService) {}

	public matchs: User[] = [];

	private _fetchMatchs() {
		this._likeService.getMatchs().subscribe({
			next: (users: User[]) => {
				this.matchs = users;
			},
			error: (error) => {
				console.error('Error fetching matches:', error);
			},
		});
	}

	public getProfilePictureUrl(userId: number): string {
		return `${environment.apiUrl}/users/${userId}/profile-picture`;
	}

	public ngOnInit(): void {
		this._fetchMatchs();
	}
}
