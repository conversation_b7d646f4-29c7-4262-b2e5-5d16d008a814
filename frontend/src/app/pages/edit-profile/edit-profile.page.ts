import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router, RouterLink } from '@angular/router';
import { AuthService } from '../../services/auth.service';
import { UsersService } from '../../services/users.service';
import { User } from '../../models/user.model';
import { GeoService } from '../../services/geo.service';
import { environment } from '../../../environments/environment';

@Component({
	selector: 'page-edit-profile',
	templateUrl: './edit-profile.page.html',
	standalone: true,
	imports: [CommonModule, FormsModule, RouterLink],
})
export class EditProfilePage implements OnInit {
	public user: User | null = null;
	public activeTab: 'basic' | 'profile' | 'pictures' = 'basic';
	public errorMessage?: string = undefined;
	public successMessage?: string = undefined;
	public passwordErrorMessage?: string = undefined;
	public passwordSuccessMessage?: string = undefined;
	public locationErrorMessage?: string = undefined;
	public locationSuccessMessage?: string = undefined;
	public newPassword: string = '';
	public confirmPassword: string = '';

	// Basic Info Form
	public basicInfoForm = {
		first_name: '',
		last_name: '',
		email: '',
	};

	// Profile Info Form
	public profileInfoForm = {
		gender: '',
		sexual_preference: [] as string[],
		biography: '',
		interests: [] as string[],
	};

	public genders: string[] = ['Man', 'Woman', 'NonBinary', 'Others'];
	public interestsInput = '';

	// Picture management
	public userPictures: { id: number; url: string; isProfile: boolean }[] = [];
	public selectedFile: File | null = null;
	public pictureErrorMessage?: string = undefined;
	public pictureSuccessMessage?: string = undefined;

	constructor(
		private authService: AuthService,
		private usersService: UsersService,
		private geoService: GeoService,
		private router: Router
	) {}

	ngOnInit() {
		this.loadUserProfile();
	}

	public updatePassword() {
		this.passwordErrorMessage = undefined;
		this.passwordSuccessMessage = undefined;

		if (!this.newPassword || !this.confirmPassword) {
			this.passwordErrorMessage = 'Please fill in both password fields';
			return;
		}

		if (this.newPassword !== this.confirmPassword) {
			this.passwordErrorMessage = 'Passwords do not match';
			return;
		}

		this.usersService.updatePassword(this.newPassword).subscribe({
			next: () => {
				this.passwordSuccessMessage = 'Password updated successfully!';
				this.newPassword = '';
				this.confirmPassword = '';
				setTimeout(() => {
					this.passwordSuccessMessage = undefined;
				}, 3000);
			},
			error: (error) => {
				this.passwordErrorMessage =
					error.error?.error || 'Failed to update password';
				setTimeout(() => {
					this.passwordErrorMessage = undefined;
				}, 4000);
			},
		});
		this.newPassword = '';
		this.confirmPassword = '';
	}

	public async updateGeolocation() {
		this.locationErrorMessage = undefined;

		if (!navigator.geolocation) {
			this.locationErrorMessage =
				'Geolocation is not supported by your browser.';
			return;
		}

		try {
			const position = await this.geoService.getCurrentPosition();
			const lat = position.coords.latitude;
			const lon = position.coords.longitude;

			const address = await this.geoService.reverseGeocode(lat, lon);
			if (address) {
				this.usersService
					.updateLocation({
						city: address.city,
						country: address.country,
						lat,
						lon,
					})
					.subscribe({
						next: () => {
							this.locationSuccessMessage = 'Location updated successfully!';
							this.locationErrorMessage = undefined;
							setTimeout(() => {
								this.locationSuccessMessage = undefined;
							}, 3000);
						},
						error: (error) => {
							this.locationErrorMessage =
								error.error?.error || 'Failed to update location';
							setTimeout(() => {
								this.locationErrorMessage = undefined;
							}, 4000);
						},
					});
			} else {
				this.locationErrorMessage =
					'Could not retrieve the address from coordinates.';
			}
		} catch (error: any) {
			if (error.code === 1) {
				this.locationErrorMessage =
					'Please allow location access to use this feature.';
			} else {
				this.locationErrorMessage =
					'An error occurred while retrieving your location.';
			}
			console.error('Error getting geolocation', error);
		}
	}

	private loadUserProfile() {
		const currentUser = this.authService.user();
		if (currentUser && currentUser.id) {
			this.usersService.profile(currentUser.id).subscribe({
				next: (user) => {
					if (user) {
						this.user = user;
						this.populateForms(user);
						this.loadUserPictures(user.id);
					}
				},
				error: (error) => {
					this.errorMessage = 'Failed to load profile';
					console.error('Error loading profile:', error);
				},
			});
		}
	}

	private populateForms(user: User) {
		// Populate basic info form
		this.basicInfoForm = {
			first_name: user.first_name || '',
			last_name: user.last_name || '',
			email: user.email || '',
		};

		// Populate profile info form
		this.profileInfoForm = {
			gender: user.gender || '',
			sexual_preference: this.parseStringToArray(user.sexual_preference),
			biography: user.biography || '',
			interests: user.interests || [],
		};
	}

	private parseStringToArray(str: string | null): string[] {
		if (!str) return [];
		// Handle PostgreSQL array format {item1,item2} or comma-separated
		return str
			.replace(/{|}/g, '')
			.split(',')
			.map((item) => item.trim())
			.filter((item) => item.length > 0);
	}

	public setActiveTab(tab: 'basic' | 'profile' | 'pictures') {
		this.activeTab = tab;
		this.clearMessages();
	}

	private clearMessages() {
		this.errorMessage = undefined;
		this.successMessage = undefined;
		this.clearPictureMessages();
	}

	public updateBasicInfo() {
		this.clearMessages();

		this.usersService.updateBasicInfo(this.basicInfoForm).subscribe({
			next: () => {
				this.successMessage = 'Basic information updated successfully!';
				this.loadUserProfile(); // Reload to get updated data
				setTimeout(() => {
					this.successMessage = undefined;
				}, 3000);
			},
			error: (error) => {
				this.errorMessage =
					error.error?.error || 'Failed to update basic information';
				setTimeout(() => {
					this.errorMessage = undefined;
				}, 4000);
			},
		});
	}

	public toggleSexualPreference(gender: string, event: Event) {
		const input = event.target as HTMLInputElement;
		const checked = input.checked;

		if (checked && !this.profileInfoForm.sexual_preference.includes(gender)) {
			this.profileInfoForm.sexual_preference = [
				...this.profileInfoForm.sexual_preference,
				gender,
			];
		} else if (!checked) {
			this.profileInfoForm.sexual_preference =
				this.profileInfoForm.sexual_preference.filter((g) => g !== gender);
		}
	}

	public addInterest(event?: Event) {
		if (event) {
			event.preventDefault();
		}
		if (!this.interestsInput.trim()) {
			return;
		}
		if (this.interestsInput.length < 2 || this.interestsInput.length > 19) {
			this.errorMessage = 'Interest must be between 2 and 19 characters';
			setTimeout(() => {
				this.errorMessage = undefined;
			}, 3000);
			return;
		}
		if (this.profileInfoForm.interests.length >= 5) {
			this.errorMessage = 'Maximum 5 interests allowed';
			setTimeout(() => {
				this.errorMessage = undefined;
			}, 3000);
			return;
		}
		const interest = '#' + this.interestsInput.trim();
		if (interest && !this.profileInfoForm.interests.includes(interest)) {
			this.profileInfoForm.interests = [
				...this.profileInfoForm.interests,
				interest,
			];
		}
		this.interestsInput = '';
	}

	public removeInterest(index: number) {
		this.profileInfoForm.interests = this.profileInfoForm.interests.filter(
			(_, i) => i !== index
		);
	}

	public updateProfileInfo() {
		this.clearMessages();

		if (this.profileInfoForm.sexual_preference.length === 0) {
			this.errorMessage = 'Please select at least one sexual preference';
			return;
		}

		this.usersService.updateProfileInfo(this.profileInfoForm).subscribe({
			next: () => {
				this.successMessage = 'Profile information updated successfully!';
				this.loadUserProfile(); // Reload to get updated data
				setTimeout(() => {
					this.successMessage = undefined;
				}, 3000);
			},
			error: (error) => {
				this.errorMessage =
					error.error?.error || 'Failed to update profile information';
				setTimeout(() => {
					this.errorMessage = undefined;
				}, 4000);
			},
		});
	}

	public goBack() {
		this.router.navigate(['/profile']);
	}

	// Picture Management Methods
	private loadUserPictures(userId: number) {
		this.usersService.getUserPictures(userId).subscribe({
			next: (response) => {
				const allPictureIds = response.pictureIds;

				// Get profile picture
				this.usersService.getUserProfilePicture(userId).subscribe({
					next: (profileBlob) => {
						const profileUrl = URL.createObjectURL(profileBlob);

						// Load all pictures and mark which one is the profile picture
						this.userPictures = allPictureIds.map(id => ({
							id,
							url: `${environment.apiUrl}/users/pictures/${id}`,
							isProfile: false
						}));

						// Add profile picture if it exists
						if (profileBlob.size > 0) {
							// Find profile picture in the list or add it
							const profilePictureExists = this.userPictures.some(pic => pic.isProfile);
							if (!profilePictureExists) {
								// Profile picture might be separate, we need to get its ID
								// For now, we'll mark the first one as profile if none are marked
								if (this.userPictures.length > 0) {
									this.userPictures[0].isProfile = true;
								}
							}
						}
					},
					error: (error) => {
						console.error('Error loading profile picture:', error);
						// Still show other pictures even if profile picture fails
						this.userPictures = allPictureIds.map(id => ({
							id,
							url: `${environment.apiUrl}/users/pictures/${id}`,
							isProfile: false
						}));
					}
				});
			},
			error: (error) => {
				console.error('Error loading pictures:', error);
				this.pictureErrorMessage = 'Failed to load pictures';
			}
		});
	}

	public onFileSelected(event: Event) {
		const input = event.target as HTMLInputElement;
		if (input.files && input.files[0]) {
			this.selectedFile = input.files[0];

			// Validate file
			if (!this.selectedFile.type.startsWith('image/')) {
				this.pictureErrorMessage = 'Only image files are allowed';
				this.selectedFile = null;
				return;
			}

			if (this.selectedFile.size > 5 * 1024 * 1024) {
				this.pictureErrorMessage = 'Image must be less than 5MB';
				this.selectedFile = null;
				return;
			}

			this.pictureErrorMessage = undefined;
		}
	}

	public uploadPicture() {
		if (!this.selectedFile) {
			this.pictureErrorMessage = 'Please select a file first';
			return;
		}

		if (this.userPictures.length >= 5) {
			this.pictureErrorMessage = 'Maximum 5 pictures allowed';
			return;
		}

		this.pictureErrorMessage = undefined;
		this.pictureSuccessMessage = undefined;

		this.usersService.uploadPicture(this.selectedFile).subscribe({
			next: (response) => {
				this.pictureSuccessMessage = 'Picture uploaded successfully!';
				this.selectedFile = null;

				// Reset file input
				const fileInput = document.getElementById('pictureInput') as HTMLInputElement;
				if (fileInput) {
					fileInput.value = '';
				}

				// Reload pictures
				if (this.user) {
					this.loadUserPictures(this.user.id);
				}

				setTimeout(() => {
					this.pictureSuccessMessage = undefined;
				}, 3000);
			},
			error: (error) => {
				this.pictureErrorMessage = error.error?.error || 'Failed to upload picture';
				setTimeout(() => {
					this.pictureErrorMessage = undefined;
				}, 4000);
			}
		});
	}

	public deletePicture(pictureId: number) {
		if (confirm('Are you sure you want to delete this picture?')) {
			this.usersService.deletePicture(pictureId).subscribe({
				next: () => {
					this.pictureSuccessMessage = 'Picture deleted successfully!';

					// Reload pictures
					if (this.user) {
						this.loadUserPictures(this.user.id);
					}

					setTimeout(() => {
						this.pictureSuccessMessage = undefined;
					}, 3000);
				},
				error: (error) => {
					this.pictureErrorMessage = error.error?.error || 'Failed to delete picture';
					setTimeout(() => {
						this.pictureErrorMessage = undefined;
					}, 4000);
				}
			});
		}
	}

	public setAsProfilePicture(pictureId: number) {
		this.usersService.setProfilePicture(pictureId).subscribe({
			next: () => {
				this.pictureSuccessMessage = 'Profile picture updated successfully!';

				// Update local state
				this.userPictures.forEach(pic => {
					pic.isProfile = pic.id === pictureId;
				});

				setTimeout(() => {
					this.pictureSuccessMessage = undefined;
				}, 3000);
			},
			error: (error) => {
				this.pictureErrorMessage = error.error?.error || 'Failed to set profile picture';
				setTimeout(() => {
					this.pictureErrorMessage = undefined;
				}, 4000);
			}
		});
	}

	private clearPictureMessages() {
		this.pictureErrorMessage = undefined;
		this.pictureSuccessMessage = undefined;
	}
}
