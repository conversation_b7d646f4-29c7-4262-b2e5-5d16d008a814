<div class="min-h-[calc(100vh-256px)] px-4 py-8">
	<div class="max-w-4xl mx-auto">
		<!-- Header -->
		<div class="flex items-center justify-between mb-8">
			<h1 class="text-4xl font-extrabold text-gray-900">Edit Profile</h1>
			<button
				(click)="goBack()"
				class="px-4 py-2 text-sm font-semibold text-purple-600 hover:text-purple-800 transition"
			>
				← Back to Profile
			</button>
		</div>

		<!-- Messages -->
		<div *ngIf="errorMessage" class="mb-6">
			<div
				class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg"
			>
				{{ errorMessage }}
			</div>
		</div>

		<div *ngIf="successMessage" class="mb-6">
			<div
				class="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg"
			>
				{{ successMessage }}
			</div>
		</div>

		<!-- Tab Navigation -->
		<div
			class="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden"
		>
			<div class="border-b border-gray-200">
				<nav class="flex">
					<button
						(click)="setActiveTab('basic')"
						[class]="activeTab === 'basic'
							? 'bg-purple-50 border-purple-500 text-purple-600'
							: 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
						class="w-1/3 py-4 px-1 text-center border-b-2 font-medium text-sm transition"
					>
						Basic Information
					</button>
					<button
						(click)="setActiveTab('profile')"
						[class]="activeTab === 'profile'
							? 'bg-purple-50 border-purple-500 text-purple-600'
							: 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
						class="w-1/3 py-4 px-1 text-center border-b-2 font-medium text-sm transition"
					>
						Profile Details
					</button>
					<button
						(click)="setActiveTab('pictures')"
						[class]="activeTab === 'pictures'
							? 'bg-purple-50 border-purple-500 text-purple-600'
							: 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
						class="w-1/3 py-4 px-1 text-center border-b-2 font-medium text-sm transition"
					>
						Pictures
					</button>
				</nav>
			</div>

			<!-- Tab Content -->
			<div class="p-8">
				<!-- Basic Information Tab -->
				<div *ngIf="activeTab === 'basic'">
					<h2 class="text-2xl font-bold text-gray-900 mb-6">
						Basic Information
					</h2>
					<p class="text-gray-600 mb-6">
						Update your personal details that are visible to other users.
					</p>

					<form (ngSubmit)="updateBasicInfo()" class="space-y-6">
						<div class="grid md:grid-cols-2 gap-6">
							<div>
								<label class="block text-sm font-medium text-gray-700 mb-2">
									First Name
								</label>
								<input
									type="text"
									[(ngModel)]="basicInfoForm.first_name"
									name="first_name"
									class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
									required
								/>
							</div>
							<div>
								<label class="block text-sm font-medium text-gray-700 mb-2">
									Last Name
								</label>
								<input
									type="text"
									[(ngModel)]="basicInfoForm.last_name"
									name="last_name"
									class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
									required
								/>
							</div>
						</div>

						<div>
							<label class="block text-sm font-medium text-gray-700 mb-2">
								Email Address
							</label>
							<input
								type="email"
								[(ngModel)]="basicInfoForm.email"
								name="email"
								class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
								required
							/>
						</div>

						<div class="flex justify-end">
							<button
								type="submit"
								class="px-6 py-3 bg-gradient text-white font-semibold rounded-lg shadow hover:from-purple-700 hover:to-orange-600 transition-all duration-200"
							>
								Update Basic Info
							</button>
						</div>
					</form>

					<hr class="my-8" />

					<h2 class="text-2xl font-bold text-gray-900 mb-6">Update password</h2>
					<p class="text-gray-600 mb-4">
						You can change your password here. Make sure to use a strong
						password.
					</p>

					<p *ngIf="passwordErrorMessage" class="text-red-500">
						{{ passwordErrorMessage}}
					</p>
					<p *ngIf="passwordSuccessMessage" class="text-green-500">
						{{ passwordSuccessMessage}}
					</p>

					<form (ngSubmit)="updatePassword()" class="space-y-6">
						<div>
							<label class="block text-sm font-medium text-gray-700 mb-2">
								New Password
							</label>
							<input
								type="password"
								[(ngModel)]="newPassword"
								name="new_password"
								class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
								required
							/>
						</div>

						<div>
							<label class="block text-sm font-medium text-gray-700 mb-2">
								Confirm New Password
							</label>
							<input
								type="password"
								[(ngModel)]="confirmPassword"
								name="confirm_new_password"
								class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
								required
							/>
						</div>

						<div class="flex justify-end">
							<button
								type="submit"
								class="px-6 py-3 bg-gradient text-white font-semibold rounded-lg shadow hover:from-purple-700 hover:to-orange-600 transition-all duration-200"
							>
								Update Password
							</button>
						</div>
					</form>
				</div>

				<!-- Profile Details Tab -->
				<div *ngIf="activeTab === 'profile'">
					<h2 class="text-2xl font-bold text-gray-900 mb-6">Profile Details</h2>
					<p class="text-gray-600 mb-6">
						Complete your dating profile information.
					</p>

					<form (ngSubmit)="updateProfileInfo()" class="space-y-6">
						<!-- Gender Selection -->
						<div>
							<label class="block text-sm font-medium text-gray-700 mb-3">
								Gender
							</label>
							<div class="grid grid-cols-2 md:grid-cols-4 gap-3">
								<label
									*ngFor="let gender of genders"
									class="relative flex items-center justify-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50 transition"
									[class.border-purple-500]="profileInfoForm.gender === gender"
									[class.bg-purple-50]="profileInfoForm.gender === gender"
								>
									<input
										type="radio"
										[(ngModel)]="profileInfoForm.gender"
										[value]="gender"
										name="gender"
										class="sr-only"
									/>
									<span class="text-sm font-medium">{{ gender }}</span>
								</label>
							</div>
						</div>

						<!-- Sexual Preferences -->
						<div>
							<label class="block text-sm font-medium text-gray-700 mb-3">
								Sexual Preferences
							</label>
							<div class="grid grid-cols-2 md:grid-cols-4 gap-3">
								<label
									*ngFor="let gender of genders"
									class="relative flex items-center justify-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50 transition"
									[class.border-orange-500]="profileInfoForm.sexual_preference.includes(gender)"
									[class.bg-orange-50]="profileInfoForm.sexual_preference.includes(gender)"
								>
									<input
										type="checkbox"
										[checked]="profileInfoForm.sexual_preference.includes(gender)"
										(change)="toggleSexualPreference(gender, $event)"
										class="sr-only"
									/>
									<span class="text-sm font-medium">{{ gender }}</span>
								</label>
							</div>
						</div>

						<!-- Biography -->
						<div>
							<label class="block text-sm font-medium text-gray-700 mb-2">
								Biography
							</label>
							<textarea
								[(ngModel)]="profileInfoForm.biography"
								name="biography"
								rows="4"
								maxlength="512"
								class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
								placeholder="Tell others about yourself..."
							></textarea>
							<p class="text-sm text-gray-500 mt-1">
								{{ profileInfoForm.biography.length }}/512 characters
							</p>
						</div>

						<!-- Interests -->
						<div>
							<label class="block text-sm font-medium text-gray-700 mb-2">
								Interests
							</label>
							<div class="flex flex-wrap gap-2 mb-3">
								<span
									*ngFor="let interest of profileInfoForm.interests; let i = index"
									class="inline-flex items-center bg-indigo-100 text-indigo-800 px-3 py-1 rounded-full text-sm font-medium"
								>
									{{ interest }}
									<button
										type="button"
										(click)="removeInterest(i)"
										class="ml-2 text-indigo-600 hover:text-indigo-800"
									>
										×
									</button>
								</span>
							</div>
							<div class="flex gap-2">
								<input
									type="text"
									[(ngModel)]="interestsInput"
									name="interestsInput"
									maxlength="19"
									class="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
									placeholder="Add an interest (without #)"
									(keyup.enter)="addInterest($event)"
								/>
								<button
									type="button"
									(click)="addInterest()"
									class="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition"
									[disabled]="profileInfoForm.interests.length >= 5"
								>
									Add
								</button>
							</div>
							<p class="text-sm text-gray-500 mt-1">
								{{ profileInfoForm.interests.length }}/5 interests
							</p>
						</div>

						<div class="flex justify-end">
							<button
								type="submit"
								class="px-6 py-3 bg-gradient text-white font-semibold rounded-lg shadow hover:from-purple-700 hover:to-orange-600 transition-all duration-200"
							>
								Update Profile Info
							</button>
						</div>
					</form>

					<hr class="m-4" />

					<h3 class="text-lg font-semibold text-gray-800 mb-4">Geolocation</h3>
					<p class="text-gray-600 mb-4">
						Your current location is automatically captured during login. You
						can update it here.
					</p>

					<p *ngIf="locationErrorMessage" class="text-red-500">
						{{ locationErrorMessage }}
					</p>

					<p *ngIf="locationSuccessMessage" class="text-green-500">
						{{ locationSuccessMessage }}
					</p>

					<div class="flex justify-end">
						<button
							(click)="updateGeolocation()"
							class="px-6 py-3 bg-gradient text-white font-semibold rounded-lg shadow hover:from-purple-700 hover:to-orange-600 transition-all duration-200"
						>
							Update Geolocation
						</button>
					</div>
				</div>

				<!-- Pictures Tab -->
				<div *ngIf="activeTab === 'pictures'">
					<h2 class="text-2xl font-bold text-gray-900 mb-6">
						Profile Pictures
					</h2>
					<p class="text-gray-600 mb-6">
						Manage your profile pictures (up to 5 pictures).
					</p>

					<!-- Error/Success Messages -->
					<div *ngIf="pictureErrorMessage" class="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
						<p class="text-red-600">{{ pictureErrorMessage }}</p>
					</div>
					<div *ngIf="pictureSuccessMessage" class="mb-4 p-4 bg-green-50 border border-green-200 rounded-lg">
						<p class="text-green-600">{{ pictureSuccessMessage }}</p>
					</div>

					<!-- Upload New Picture -->
					<div class="mb-8 p-6 bg-gray-50 rounded-lg border">
						<h3 class="text-lg font-semibold text-gray-900 mb-4">Upload New Picture</h3>
						<div class="space-y-4">
							<div>
								<input
									type="file"
									id="pictureInput"
									accept="image/*"
									(change)="onFileSelected($event)"
									class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-semibold file:bg-purple-50 file:text-purple-700 hover:file:bg-purple-100"
								/>
								<p class="text-sm text-gray-500 mt-2">
									Accepted formats: JPEG, PNG, WEBP. Maximum size: 5MB.
								</p>
							</div>
							<button
								type="button"
								(click)="uploadPicture()"
								[disabled]="!selectedFile || userPictures.length >= 5"
								class="px-4 py-2 bg-gradient text-white font-semibold rounded-lg shadow hover:from-purple-700 hover:to-orange-600 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
							>
								Upload Picture
							</button>
							<p *ngIf="userPictures.length >= 5" class="text-sm text-orange-600">
								You have reached the maximum of 5 pictures.
							</p>
						</div>
					</div>

					<!-- Current Pictures -->
					<div>
						<h3 class="text-lg font-semibold text-gray-900 mb-4">
							Your Pictures ({{ userPictures.length }}/5)
						</h3>

						<div *ngIf="userPictures.length === 0" class="text-center py-8 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
							<p class="text-gray-500">No pictures uploaded yet.</p>
							<p class="text-sm text-gray-400 mt-2">Upload your first picture to get started!</p>
						</div>

						<div *ngIf="userPictures.length > 0" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
							<div
								*ngFor="let picture of userPictures"
								class="relative bg-white rounded-lg shadow-md overflow-hidden border"
							>
								<!-- Picture -->
								<div class="aspect-square">
									<img
										[src]="picture.url"
										[alt]="'Picture ' + picture.id"
										class="w-full h-full object-cover"
										loading="lazy"
									/>
								</div>

								<!-- Profile Picture Badge -->
								<div
									*ngIf="picture.isProfile"
									class="absolute top-2 left-2 bg-purple-600 text-white text-xs font-semibold px-2 py-1 rounded-full"
								>
									Profile Picture
								</div>

								<!-- Actions -->
								<div class="p-4 space-y-2">
									<button
										*ngIf="!picture.isProfile"
										type="button"
										(click)="setAsProfilePicture(picture.id)"
										class="w-full px-3 py-2 bg-purple-100 text-purple-700 font-medium rounded-lg hover:bg-purple-200 transition-colors"
									>
										Set as Profile Picture
									</button>
									<button
										type="button"
										(click)="deletePicture(picture.id)"
										class="w-full px-3 py-2 bg-red-100 text-red-700 font-medium rounded-lg hover:bg-red-200 transition-colors"
									>
										Delete Picture
									</button>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
