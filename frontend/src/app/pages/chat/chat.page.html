<div
	class="min-h-[calc(100vh-256px)] flex flex-col bg-white rounded-2xl shadow-lg border border-purple-200 my-4"
>
	<!-- Chat Header -->
	<div
		class="flex items-center justify-between p-4 border-b border-gray-200 bg-gradient-to-r from-purple-50 to-orange-50 rounded-t-2xl"
	>
		<div class="flex items-center space-x-3">
			<button
				(click)="goBack()"
				class="p-2 hover:bg-white hover:bg-opacity-50 rounded-full transition-colors"
				aria-label="Go back"
			>
				<svg
					class="w-5 h-5 text-gray-600"
					fill="none"
					stroke="currentColor"
					viewBox="0 0 24 24"
				>
					<path
						stroke-linecap="round"
						stroke-linejoin="round"
						stroke-width="2"
						d="M15 19l-7-7 7-7"
					></path>
				</svg>
			</button>

			<div *ngIf="recipientUser" class="flex items-center space-x-3">
				<img
					[src]="getProfilePictureUrl(recipientUser.id)"
					[alt]="recipientUser.first_name + ' ' + recipientUser.last_name"
					class="w-10 h-10 rounded-full object-cover border-2 border-white shadow-sm"
					onerror="this.onerror=null; this.src='/images/default-profile.png';"
				/>
				<div>
					<h2 class="text-lg font-semibold text-gray-900">
						{{ recipientUser.first_name }} {{ recipientUser.last_name }}
					</h2>
				</div>
			</div>

			<div
				*ngIf="!recipientUser && !isLoading"
				class="flex items-center space-x-3"
			>
				<div class="w-10 h-10 bg-gray-200 rounded-full animate-pulse"></div>
				<div>
					<div class="h-4 bg-gray-200 rounded w-24 animate-pulse"></div>
					<div class="h-3 bg-gray-200 rounded w-16 mt-1 animate-pulse"></div>
				</div>
			</div>
		</div>

		<div *ngIf="recipientUser" class="flex items-center space-x-2">
			<a
				[routerLink]="['/profile']"
				[queryParams]="{ id: recipientUser.id }"
				class="px-3 py-1 text-sm bg-purple-100 text-purple-700 rounded-full hover:bg-purple-200 transition-colors"
			>
				View Profile
			</a>
		</div>
	</div>

	<!-- Error Message -->
	<div *ngIf="errorMessage" class="mx-4 mt-4">
		<div class="bg-red-50 border border-red-200 rounded-lg p-3">
			<div class="flex items-center justify-between mb-2">
				<div class="flex items-center space-x-2">
					<svg
						class="w-5 h-5 text-red-500"
						fill="none"
						stroke="currentColor"
						viewBox="0 0 24 24"
					>
						<path
							stroke-linecap="round"
							stroke-linejoin="round"
							stroke-width="2"
							d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
						></path>
					</svg>
					<span class="text-red-700 text-sm">{{ errorMessage }}</span>
				</div>
				<button
					(click)="clearError()"
					class="text-red-500 hover:text-red-700 transition-colors"
				>
					<svg
						class="w-4 h-4"
						fill="none"
						stroke="currentColor"
						viewBox="0 0 24 24"
					>
						<path
							stroke-linecap="round"
							stroke-linejoin="round"
							stroke-width="2"
							d="M6 18L18 6M6 6l12 12"
						></path>
					</svg>
				</button>
			</div>
			<div
				*ngIf="errorMessage.includes('Connection') || errorMessage.includes('Disconnected')"
				class="flex justify-end"
			>
				<button
					(click)="retryConnection()"
					class="px-3 py-1 text-xs bg-red-100 text-red-700 rounded hover:bg-red-200 transition-colors"
				>
					Retry Connection
				</button>
			</div>
		</div>
	</div>

	<!-- Loading State -->
	<div *ngIf="isLoading" class="flex-1 flex items-center justify-center">
		<div class="text-center">
			<div
				class="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"
			></div>
			<p class="text-gray-600">Loading conversation...</p>
		</div>
	</div>

	<!-- Messages Container -->
	<div
		*ngIf="!isLoading"
		#messagesContainer
		class="flex-1 overflow-y-auto p-4 space-y-4 bg-gray-50"
		style="max-height: calc(100vh - 400px)"
	>
		<!-- No Messages State -->
		<div *ngIf="messages.length === 0" class="text-center py-12">
			<div
				class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4"
			>
				<svg
					class="w-8 h-8 text-purple-600"
					fill="none"
					stroke="currentColor"
					viewBox="0 0 24 24"
				>
					<path
						stroke-linecap="round"
						stroke-linejoin="round"
						stroke-width="2"
						d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
					></path>
				</svg>
			</div>
			<h3 class="text-lg font-semibold text-gray-900 mb-2">
				Start the conversation!
			</h3>
			<p class="text-gray-600">
				Send a message to begin chatting with {{ recipientUser?.first_name ||
				'this user' }}.
			</p>
		</div>

		<!-- Messages -->
		<div
			*ngFor="let msg of messages; trackBy: trackByMessageId"
			class="flex"
			[class.justify-end]="isMyMessage(msg)"
		>
			<div
				class="max-w-xs lg:max-w-md px-4 py-2 rounded-2xl shadow-sm"
				[class]="isMyMessage(msg)
					? 'bg-gradient-to-r from-purple-600 to-orange-500 text-white rounded-br-md'
					: 'bg-white text-gray-900 border border-gray-200 rounded-bl-md'"
			>
				<p class="text-sm break-words" [innerHTML]="msg.content"></p>
				<div
					class="text-xs mt-1 opacity-75"
					[class]="isMyMessage(msg) ? 'text-purple-100' : 'text-gray-500'"
				>
					{{ formatTime(msg.created_at) }}
				</div>
			</div>
		</div>
	</div>

	<!-- Message Input -->
	<div
		*ngIf="!isLoading"
		class="border-t border-gray-200 p-4 bg-white rounded-b-2xl"
	>
		<div class="flex items-end space-x-3">
			<div class="flex-1">
				<textarea
					[(ngModel)]="message"
					(keydown)="onKeyPress($event)"
					placeholder="Type your message..."
					rows="1"
					maxlength="100"
					class="w-full px-4 py-3 border border-gray-300 rounded-2xl focus:outline-none focus:ring-2 focus:ring-purple-400 focus:border-transparent resize-none"
					[disabled]="isSending"
				></textarea>
				<div class="flex justify-between items-center mt-1 px-2">
					<span class="text-xs text-gray-500">
						{{ message.length }}/100 characters
					</span>
				</div>
			</div>
			<button
				(click)="sendMessage()"
				[disabled]="!message.trim() || isSending"
				class="p-3 bg-gradient-to-r from-purple-600 to-orange-500 text-white rounded-2xl hover:from-purple-700 hover:to-orange-600 focus:outline-none focus:ring-2 focus:ring-purple-400 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg"
			>
				<svg
					*ngIf="!isSending"
					class="w-5 h-5"
					fill="none"
					stroke="currentColor"
					viewBox="0 0 24 24"
				>
					<path
						stroke-linecap="round"
						stroke-linejoin="round"
						stroke-width="2"
						d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"
					></path>
				</svg>
				<div
					*ngIf="isSending"
					class="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"
				></div>
			</button>
		</div>
	</div>
</div>
