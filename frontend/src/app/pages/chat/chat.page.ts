import {
	<PERSON>mpo<PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON>ement<PERSON>ef,
	ViewChild,
	AfterViewChecked,
} from '@angular/core';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { LikeService } from '../../services/like.service';
import { CommonModule } from '@angular/common';
import { environment } from '../../../environments/environment';
import { StorageService } from '../../services/storage.service';
import { FormsModule } from '@angular/forms';
import { Message } from '../../models/message.model';
import { MessageService } from '../../services/message.service';
import { AuthService } from '../../services/auth.service';
import { SanitizationService } from '../../services/sanitization.service';
import { UsersService } from '../../services/users.service';

@Component({
	selector: 'page-chat',
	templateUrl: './chat.page.html',
	standalone: true,
	imports: [CommonModule, FormsModule, RouterLink],
})
export class ChatPage implements <PERSON><PERSON><PERSON>t, OnD<PERSON>roy, AfterViewChecked {
	@ViewChild('messagesContainer') messagesContainer!: ElementRef;

	constructor(
		private _likeService: LikeService,
		private _messageService: MessageService,
		private _route: ActivatedRoute,
		private _router: Router,
		private _storageService: StorageService,
		private _authService: AuthService,
		private _sanitizationService: SanitizationService,
		private _usersService: UsersService
	) {}

	private _pingIntervalId: any;
	public recipientUserId: number = 0;
	public fromUserId: number = 0;
	public message: string = '';
	public messages: Message[] = [];
	public recipientUser: any = null;
	public isLoading: boolean = true;
	public errorMessage: string = '';
	public isSending: boolean = false;
	private _socket: WebSocket | null = null;
	private _shouldScrollToBottom: boolean = false;

	private _orderMessagesByDate() {
		this.messages.sort((a, b) => {
			return (
				new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
			);
		});
	}

	private _connectWebSocket() {
		if (this._socket) {
			this._socket.close();
		}

		this.errorMessage = '';

		this._socket = new WebSocket(
			`${environment.websocketUrl}/chat?userId=${
				this.recipientUserId
			}&token=${this._storageService.getToken()}`
		);

		this._socket.onopen = () => {
			this.errorMessage = '';
		};

		this._socket.onmessage = (event) => {
			try {
				const data = JSON.parse(event.data);

				// Handle error messages from server
				if (data.error) {
					this.errorMessage = data.error;
					setTimeout(() => (this.errorMessage = ''), 5000);
					return;
				}

				// Handle regular messages
				const newMessage: Message = data;
				// Sanitize incoming message content
				newMessage.content = this._sanitizationService.sanitizeText(
					newMessage.content
				);
				this.messages.push(newMessage);
				this._orderMessagesByDate();
				this._shouldScrollToBottom = true;
			} catch (error) {
				console.error('Error parsing message:', error);
				this.errorMessage = 'Error receiving message';
				setTimeout(() => (this.errorMessage = ''), 3000);
			}
		};

		this._socket.onerror = (error) => {
			console.error('WebSocket error:', error);
			this.errorMessage = 'Connection error. Please refresh the page.';
		};

		this._socket.onclose = () => {
			if (!this.errorMessage) {
				this.errorMessage = 'Connection lost. Please refresh the page.';
			}
		};
	}

	public sendMessage() {
		if (this.isSending) return;

		// Validate and sanitize message
		const processedMessage = this._sanitizationService.processMessage(
			this.message
		);
		if (!processedMessage.isValid) {
			this.errorMessage = processedMessage.error || 'Invalid message';
			setTimeout(() => (this.errorMessage = ''), 3000);
			return;
		}

		if (!this._socket || this._socket.readyState !== WebSocket.OPEN) {
			this.errorMessage = 'Connection lost. Please refresh the page.';
			setTimeout(() => (this.errorMessage = ''), 3000);
			return;
		}

		this.isSending = true;
		this.errorMessage = '';

		try {
			this._socket.send(
				JSON.stringify({
					content: processedMessage.sanitized,
				})
			);

			// Add message to local state
			this.messages.push({
				from_user_id: this.fromUserId,
				to_user_id: this.recipientUserId,
				content: processedMessage.sanitized,
				created_at: new Date(),
			});

			this._orderMessagesByDate();
			this._shouldScrollToBottom = true;
			this.message = '';
		} catch (error) {
			this.errorMessage = 'Failed to send message. Please try again.';
			setTimeout(() => (this.errorMessage = ''), 3000);
		} finally {
			this.isSending = false;
		}
	}

	private _fetchMessages() {
		this._messageService.getMessages(this.recipientUserId).subscribe({
			next: (messages: Message[]) => {
				// Sanitize all message content
				this.messages = messages.map((msg) => ({
					...msg,
					content: this._sanitizationService.sanitizeText(msg.content),
				}));
				this._orderMessagesByDate();
				this._shouldScrollToBottom = true;
				this.isLoading = false;
			},
			error: (error: any) => {
				console.error('Error fetching messages:', error);
				this.errorMessage = 'Failed to load messages';
				this.isLoading = false;
			},
		});
	}

	private _isMutualLike() {
		this._likeService.isMutualLike(this.recipientUserId).subscribe({
			next: (response: { mutual: boolean }) => {
				if (!response.mutual) {
					this._router.navigate(['/match']);
					console.error('This user is taken, sorry...');
				} else {
					this._loadRecipientUser();
					this._connectWebSocket();
					this._fetchMessages();
				}
			},
			error: (error: any) => {
				console.error('Error checking mutual like:', error);
				this.errorMessage = 'Unable to verify chat permissions';
				this._router.navigate(['/match']);
			},
		});
	}

	private _loadRecipientUser() {
		this._usersService.profile(this.recipientUserId).subscribe({
			next: (user) => {
				this.recipientUser = user;
			},
			error: (error: any) => {
				console.error('Error loading recipient user:', error);
				this.errorMessage = 'Unable to load user information';
			},
		});
	}

	public getProfilePictureUrl(userId: number): string {
		return `${environment.apiUrl}/users/${userId}/profile-picture`;
	}

	private _scrollToBottom(): void {
		try {
			if (this.messagesContainer) {
				this.messagesContainer.nativeElement.scrollTop =
					this.messagesContainer.nativeElement.scrollHeight;
			}
		} catch (err) {
			console.error('Error scrolling to bottom:', err);
		}
	}

	public formatTime(date: Date): string {
		return new Date(date).toLocaleTimeString([], {
			hour: '2-digit',
			minute: '2-digit',
		});
	}

	public isMyMessage(message: Message): boolean {
		return message.from_user_id === this.fromUserId;
	}

	public onKeyPress(event: KeyboardEvent): void {
		if (event.key === 'Enter' && !event.shiftKey) {
			event.preventDefault();
			this.sendMessage();
		}
	}

	public retryConnection(): void {
		this.errorMessage = '';
		this.isLoading = true;
		this._isMutualLike();
	}

	public clearError(): void {
		this.errorMessage = '';
	}

	public trackByMessageId(index: number, message: Message): any {
		return (
			message.created_at.toString() + message.from_user_id + message.content
		);
	}

	public goBack(): void {
		this._router.navigate(['/match']);
	}

	private _startPinging(): void {
		this._pingIntervalId = setInterval(() => {
			if (this._socket && this._socket.readyState === WebSocket.OPEN) {
				this._socket.send(JSON.stringify({ type: 'ping' }));
			}
		}, 30000); // every 30 seconds
	}

	private _stopPinging(): void {
		if (this._pingIntervalId) {
			clearInterval(this._pingIntervalId);
			this._pingIntervalId = null;
		}
	}

	public ngOnInit(): void {
		this._route.params.subscribe((params: any) => {
			this.recipientUserId = parseInt(params['userId'], 10);
			if (isNaN(this.recipientUserId)) {
				this._router.navigate(['/match']);
				return;
			}
		});

		const currentUser = this._authService.user();
		if (!currentUser) {
			this._router.navigate(['/login']);
			return;
		}

		this.fromUserId = currentUser.id;
		this._isMutualLike();
		this._startPinging();
	}

	public ngAfterViewChecked(): void {
		if (this._shouldScrollToBottom) {
			this._scrollToBottom();
			this._shouldScrollToBottom = false;
		}
	}

	public ngOnDestroy(): void {
		if (this._socket) {
			this._socket.close();
			this._socket = null;
		}
		this._stopPinging();
	}
}
