import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { AuthService } from '../../services/auth.service';

@Component({
	selector: 'page-forgot-password',
	templateUrl: './forgot-password.page.html',
	standalone: true,
	imports: [CommonModule, FormsModule],
})
export class ForgotPasswordPage {
	constructor(private _authService: AuthService) {}

	public email: string = '';

	public errorMessage: string | null = null;
	public successMessage: string | null = null;

	public handleForgotPassword(e: Event): void {
		e.preventDefault();

		if (!this.email) {
			this.errorMessage = 'Email is required';
			return;
		}

		this.errorMessage = null;
		this.successMessage = null;
		this._authService.forgotPassword(this.email).subscribe({
			next: (response) => {
				this.successMessage = response.message;
				setTimeout(() => {
					this.successMessage = null;
				}, 5000);
			},
			error: (error) => {
				this.errorMessage = error.error?.error || 'An error occurred';
			},
		});
	}
}
