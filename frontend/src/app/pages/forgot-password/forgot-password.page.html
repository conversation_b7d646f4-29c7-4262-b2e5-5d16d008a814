<div class="min-h-[calc(100vh-256px)] flex items-center justify-center px-4">
	<form
		(submit)="handleForgotPassword($event)"
		class="w-full max-w-md bg-white rounded-2xl shadow-lg p-8 border border-purple-200"
	>
		<h2
			class="text-3xl font-extrabold text-purple-700 mb-6 text-center drop-shadow"
		>
			Forgot password
		</h2>

		<p *ngIf="errorMessage" class="text-red-500 text-sm mb-4 text-center">
			{{ errorMessage }}
		</p>
		<p *ngIf="successMessage" class="text-green-500 text-sm mb-4 text-center">
			{{ successMessage }}
		</p>

		<div class="mb-4">
			<input
				type="text"
				class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
				[(ngModel)]="email"
				name="email"
				autocomplete="off"
				placeholder="Enter your email"
				required
			/>
		</div>

		<button
			type="submit"
			class="w-full py-3 rounded-lg bg-gradient text-white font-semibold shadow hover:from-purple-700 hover:to-orange-600 transition-all duration-200 disabled:cursor-not-allowed"
			[disabled]="!email"
		>
			Send reset link
		</button>
	</form>
</div>
