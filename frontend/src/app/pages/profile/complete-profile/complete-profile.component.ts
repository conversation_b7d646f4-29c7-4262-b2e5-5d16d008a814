import { Component, EventEmitter, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { UsersService } from '../../../services/users.service';
import { CompleteProfile } from '../../../models/complete-profile.model';

@Component({
	selector: 'component-complete-profile',
	templateUrl: './complete-profile.component.html',
	standalone: true,
	imports: [CommonModule, FormsModule],
})
export class CompleteProfileComponent {
	constructor(private _usersService: UsersService) {}

	@Output() profileCompleted = new EventEmitter<void>();

	public errorMessage: string | null = null;

	public genders: CompleteProfile['gender'][] = [
		'Man',
		'Woman',
		'NonBinary',
		'Others',
	];

	public interestsInput = '';
	public selectedFiles: File[] = [];
	public imagePreviews: string[] = [];

	public completeProfileRequest: CompleteProfile = {
		gender: 'Man',
		sexual_preference: [],
		biography: '',
		interests: [],
		birthdate: new Date(),
	};

	public onFilesSelected(event: Event) {
		const input = event.target as HTMLInputElement;
		if (!input.files) return;

		const files = Array.from(input.files);

		if (files.length + this.selectedFiles.length > 5) {
			this.errorMessage = 'You can only upload up to 5 images';
			setTimeout(() => (this.errorMessage = null), 3000);
			return;
		}

		for (const file of files) {
			if (!file.type.startsWith('image/')) {
				this.errorMessage = 'Only image files are allowed';
				setTimeout(() => (this.errorMessage = null), 3000);
				continue;
			}

			this.selectedFiles.push(file);
			const reader = new FileReader();
			reader.onload = () => {
				if (typeof reader.result === 'string') {
					this.imagePreviews.push(reader.result);
				}
			};
			reader.readAsDataURL(file);
		}
	}

	public selectGender(gender: CompleteProfile['gender']) {
		this.completeProfileRequest.gender = gender;
	}

	public toggleSexualPreference(
		gender: CompleteProfile['gender'],
		event: Event
	) {
		const input = event.target as HTMLInputElement | null;
		if (!input) return;

		const checked = input.checked;
		if (
			checked &&
			!this.completeProfileRequest.sexual_preference.includes(gender)
		) {
			this.completeProfileRequest.sexual_preference = [
				...this.completeProfileRequest.sexual_preference,
				gender,
			];
		} else if (!checked) {
			this.completeProfileRequest.sexual_preference =
				this.completeProfileRequest.sexual_preference.filter(
					(g) => g !== gender
				);
		}
	}

	public updateInterests() {
		this.completeProfileRequest.interests = this.interestsInput
			.split(',')
			.map((i) => i.trim())
			.filter((i) => i.length > 0);
	}

	public addInterest(event?: Event) {
		if (event) {
			event.preventDefault();
		}
		if (!this.interestsInput.trim()) {
			return;
		}
		if (this.interestsInput.length < 1 || this.interestsInput.length > 19) {
			this.errorMessage = 'Interest must be between 1 and 19 characters';
			setTimeout(() => {
				this.errorMessage = null;
			}, 3000);
			return;
		}
		if (this.completeProfileRequest.interests.length >= 5) {
			this.errorMessage = 'Maximum 5 interests allowed';
			setTimeout(() => {
				this.errorMessage = null;
			}, 3000);
			return;
		}
		const interest = '#' + this.interestsInput.trim();
		if (interest && !this.completeProfileRequest.interests.includes(interest)) {
			this.completeProfileRequest.interests = [
				...this.completeProfileRequest.interests,
				interest,
			];
		}
		this.interestsInput = '';
	}

	public removeInterest(index: number) {
		this.completeProfileRequest.interests =
			this.completeProfileRequest.interests.filter((_, i) => i !== index);
	}

	public removeSelectedImage(index: number) {
		this.selectedFiles.splice(index, 1);
		this.imagePreviews.splice(index, 1);
	}

	public submitProfile() {
		const formData = new FormData();
		formData.append('gender', this.completeProfileRequest.gender);
		formData.append(
			'sexual_preference',
			JSON.stringify(this.completeProfileRequest.sexual_preference)
		);
		formData.append('biography', this.completeProfileRequest.biography);
		formData.append(
			'interests',
			JSON.stringify(this.completeProfileRequest.interests)
		);
		const birthdate =
			this.completeProfileRequest.birthdate instanceof Date
				? this.completeProfileRequest.birthdate.toISOString()
				: new Date(this.completeProfileRequest.birthdate).toISOString();
		formData.append('birthdate', birthdate);

		this.selectedFiles.forEach((file) => {
			formData.append('pictures', file);
		});

		this._usersService.completeProfile(formData).subscribe({
			next: () => {
				this.profileCompleted.emit();
			},
			error: (error) => {
				this.errorMessage =
					error.error?.error ||
					'An error occurred while completing the profile';
			},
		});
	}
}
