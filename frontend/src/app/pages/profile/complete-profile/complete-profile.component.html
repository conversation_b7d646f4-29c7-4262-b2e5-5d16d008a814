<div class="min-h-[calc(100vh-256px)] flex items-center justify-center px-4">
	<form
		(ngSubmit)="submitProfile()"
		class="w-full max-w-xl bg-white rounded-2xl shadow-lg p-8 border border-purple-200"
	>
		<h2
			class="text-3xl font-extrabold text-purple-700 mb-6 text-center drop-shadow"
		>
			Complete Your Profile
		</h2>
		<!-- Error Message -->
		<div *ngIf="errorMessage" class="text-red-600 text-center">
			<p>{{ errorMessage }}</p>
		</div>

		<!-- Birthdate -->
		<div class="mb-6">
			<label class="block mb-2 font-semibold text-gray-700">Birthdate:</label>
			<input
				type="date"
				[(ngModel)]="completeProfileRequest.birthdate"
				name="birthdate"
				class="w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
				required
			/>
		</div>

		<!-- Gender (Single Selection) -->
		<div class="mb-6">
			<label class="block mb-2 font-semibold text-gray-700">Gender:</label>
			<div class="flex flex-wrap gap-4">
				<label
					*ngFor="let g of genders"
					class="inline-flex items-center cursor-pointer"
				>
					<input
						type="checkbox"
						[name]="'gender'"
						[value]="g"
						[checked]="completeProfileRequest.gender === g"
						(change)="selectGender(g)"
						class="form-checkbox h-5 w-5 text-blue-600"
					/>
					<span class="ml-2 text-gray-800 select-none">{{ g }}</span>
				</label>
			</div>
		</div>

		<!-- Sexual Preference (Multi Selection) -->
		<div class="mb-6">
			<label class="block mb-2 font-semibold text-gray-700"
				>Sexual Preferences:</label
			>
			<div class="flex flex-wrap gap-6">
				<label
					*ngFor="let g of genders"
					class="inline-flex items-center cursor-pointer"
				>
					<input
						type="checkbox"
						[value]="g"
						[checked]="completeProfileRequest.sexual_preference.includes(g)"
						(change)="toggleSexualPreference(g, $event)"
						class="form-checkbox h-5 w-5 text-blue-600"
					/>
					<span class="ml-2 text-gray-800 select-none">{{ g }}</span>
				</label>
			</div>
		</div>

		<!-- Biography -->
		<div>
			<label class="block mb-2 font-semibold text-gray-700">Biography:</label>
			<textarea
				rows="4"
				[(ngModel)]="completeProfileRequest.biography"
				name="biography"
				class="w-full border border-gray-300 rounded px-3 py-2 resize-y focus:outline-none focus:ring-2 focus:ring-blue-500"
				placeholder="Tell us about yourself..."
				maxlength="256"
			></textarea>
		</div>

		<!-- Interests -->
		<div>
			<label class="block mb-2 font-semibold text-gray-700">Interests:</label>
			<div class="flex flex-col xs:flex-row gap-2 mb-3">
				<input
					type="text"
					[(ngModel)]="interestsInput"
					name="interests"
					placeholder="Add an interest"
					(keydown.enter)="addInterest($event)"
					class="flex-grow border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
					maxlength="19"
				/>
				<button
					type="button"
					(click)="addInterest()"
					class="bg-blue-600 hover:bg-blue-700 text-white px-4 rounded transition w-full xs:w-auto"
					aria-label="Add interest"
				>
					+
				</button>
			</div>

			<div class="flex flex-wrap gap-2 mb-8">
				<span
					*ngFor="
						let interest of completeProfileRequest.interests;
						let i = index
					"
					class="bg-blue-100 text-blue-800 rounded-full px-4 py-1 flex items-center space-x-2"
				>
					<span>{{ interest }}</span>
					<button
						type="button"
						(click)="removeInterest(i)"
						class="text-blue-600 hover:text-blue-900 font-bold focus:outline-none"
						aria-label="Remove interest"
					>
						&times;
					</button>
				</span>
			</div>
		</div>

		<!-- Upload Images -->
		<div class="mb-6">
			<label class="block mb-2 font-semibold text-gray-700"
				>Upload up to 5 pictures:</label
			>
			<input
				type="file"
				(multiple)="(true)"
				(change)="onFilesSelected($event)"
				accept="image/*"
				class="w-full mb-4"
			/>

			<!-- Preview thumbnails -->
			<div class="flex flex-wrap gap-4 justify-center">
				<div
					*ngFor="let preview of imagePreviews; let i = index"
					class="relative w-24 h-24 rounded overflow-hidden border"
				>
					<img
						[src]="preview"
						alt="Preview"
						class="object-cover w-full h-full"
					/>
					<button
						type="button"
						(click)="removeSelectedImage(i)"
						class="absolute -top-1 -right-1 bg-orange-600 text-white rounded-full w-6 h-6 font-bold text-xs flex items-center justify-center shadow hover:bg-purple-700"
						title="Remove"
					>
						&times;
					</button>
				</div>
			</div>
		</div>

		<!-- Submit -->
		<button
			type="submit"
			class="w-full py-3 rounded-lg bg-gradient text-white font-semibold shadow hover:from-purple-700 hover:to-orange-600 transition-all duration-200 disabled:cursor-not-allowed"
		>
			Complete Profile
		</button>
	</form>
</div>
