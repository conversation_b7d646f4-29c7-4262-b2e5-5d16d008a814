<h1 class="text-3xl font-bold mb-6 text-center text-gray-800">User Profile</h1>

<div
	*ngIf="user"
	class="max-w-3xl mx-auto p-6 bg-white rounded-2xl shadow-lg space-y-4 relative"
>
	<button *ngIf="!isMyProfile" class="absolute top-3 right-3 font-bold text-xs">
		LIKE
	</button>
	<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
		<p>
			<span class="font-semibold text-gray-700">First Name:</span> {{
			user.first_name }}
		</p>
		<p>
			<span class="font-semibold text-gray-700">Last Name:</span> {{
			user.last_name }}
		</p>
		<p>
			<span class="font-semibold text-gray-700">Username:</span> {{
			user.username }}
		</p>
		<p>
			<span class="font-semibold text-gray-700">Gender:</span> {{ user.gender ||
			'—' }}
		</p>
		<p>
			<span class="font-semibold text-gray-700">Sexual Preference:</span>
			{{ user.sexual_preference ? user.sexual_preference : '—' }}
		</p>
		<p>
			<span class="font-semibold text-gray-700">Interests:</span>
			<ng-container
				*ngIf="user.interests && user.interests.length; else noInterests"
			>
				<span *ngFor="let interest of user.interests; let last = last">
					{{ interest }}<span *ngIf="!last">, </span>
				</span>
			</ng-container>
			<ng-template #noInterests>—</ng-template>
		</p>
		<p>
			<span class="font-semibold text-gray-700">Created At:</span>
			{{ user.created_at | date: 'medium' }}
		</p>
	</div>

	<p *ngIf="user.biography" class="text-gray-700 whitespace-pre-line">
		<span class="font-semibold">Biography:</span> {{ user.biography }}
	</p>

	<!-- Map Embed -->
	<div
		*ngIf="user.location.x !== null && user.location.y !== null"
		class="mt-6 rounded-xl overflow-hidden shadow-md border"
	>
		<iframe
			class="w-full h-72"
			frameborder="0"
			[title]="'Map of user location'"
			[src]="mapUrl"
			allowfullscreen
		></iframe>
	</div>
</div>
