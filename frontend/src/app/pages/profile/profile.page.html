<!-- Show Complete Profile Component if needed -->
<ng-container *ngIf="!isMyProfileCompleted && isMyProfile; else completed">
	<component-complete-profile (profileCompleted)="profileCompleted()" />
</ng-container>

<!-- Profile Section -->
<ng-template #completed>
	<h1 class="text-4xl font-extrabold mb-10 text-center text-gray-900">
		<ng-container *ngIf="isMyProfile; else notMyProfile"
			>My Profile</ng-container
		>
		<ng-template #notMyProfile>
			{{ user?.first_name }} {{ user?.last_name }}'s Profile
		</ng-template>
	</h1>

	<div
		*ngIf="user"
		class="max-w-5xl mx-auto p-6 sm:p-10 bg-white rounded-3xl shadow-xl border border-gray-100 space-y-10 relative"
	>
		<!-- Like Button -->
		<button
			*ngIf="!isMyProfile"
			class="absolute top-5 right-5 px-4 py-1 text-sm font-semibold text-white bg-purple-600 hover:bg-orange-500 transition rounded-full shadow"
		>
			❤️ LIKE
		</button>
		<a
			*ngIf="isMyProfile"
			routerLink="/profile/edit"
			class="absolute top-5 right-5 px-4 py-1 text-sm font-semibold text-white bg-purple-600 hover:bg-orange-500 transition rounded-full shadow"
		>
			⚙️ Edit Profile
		</a>

		<!-- Profile Picture -->
		<div class="flex justify-center">
			<img
				[src]="getProfilePictureUrl(user.id)"
				alt="Profile picture"
				class="w-40 h-40 rounded-full object-cover border-4 border-white shadow-md ring-2 ring-purple-500 transition hover:scale-105 duration-200"
				onerror="this.onerror=null; this.src='/images/default-profile.png';"
			/>
		</div>

		<!-- User Info -->
		<div class="grid md:grid-cols-2 gap-4 text-gray-700 text-base">
			<p class="font-semibold text-lg text-gray-900">
				{{ user.first_name }} {{ user.last_name }} ({{ user.username }})
			</p>
			<p>{{ user.birthdate | date: 'longDate' }} ({{ age }} years old)</p>
			<p><span class="font-semibold">Gender:</span> {{ user.gender || '—' }}</p>
			<p>
				<span class="font-semibold">Sexual Preference:</span>
				{{ user.sexual_preference || '—' }}
			</p>
			<p>
				<span class="font-semibold">Created At:</span>
				{{ user.created_at | date: 'medium' }}
			</p>
		</div>

		<!-- Biography & Interests -->
		<div *ngIf="user.biography" class="text-gray-700 space-y-4">
			<h2 class="text-xl font-bold text-gray-900">📝 Biography</h2>
			<p
				class="bg-gray-50 p-4 rounded-xl border border-gray-200 leading-relaxed"
			>
				{{ user.biography }}
			</p>

			<div *ngIf="user.interests?.length" class="pt-2">
				<h3 class="text-lg font-semibold text-gray-800 mb-2">🎯 Interests</h3>
				<div class="flex flex-wrap gap-2">
					<span
						*ngFor="let interest of user.interests"
						class="inline-block bg-indigo-100 text-indigo-800 px-3 py-1 rounded-full text-sm font-medium"
					>
						{{ interest }}
					</span>
				</div>
			</div>
		</div>

		<!-- Location Section -->
		<div *ngIf="mapUrl" class="space-y-4">
			<h2 class="text-xl font-bold text-gray-900">📍 Location</h2>
			<div class="rounded-xl overflow-hidden shadow border border-gray-200">
				<iframe
					[src]="mapUrl"
					width="100%"
					height="300"
					style="border: 0"
					allowfullscreen
					loading="lazy"
					referrerpolicy="no-referrer-when-downgrade"
				></iframe>
			</div>
			<p class="text-center text-gray-600">
				{{ user.city }}, {{ user.country }}
			</p>
			<p class="text-center text-sm text-gray-500">
				Longitude: {{ user.longitude }} | Latitude: {{ user.latitude }}
			</p>
		</div>
	</div>

	<!-- User Pictures Section -->
	<div
		*ngIf="pictureUrls && pictureUrls.length > 0"
		class="max-w-5xl mx-auto p-6 sm:p-10 bg-white rounded-3xl shadow-xl border border-gray-100 mt-10 flex justify-center"
	>
		<div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
			<img
				*ngFor="let url of pictureUrls"
				[src]="url"
				alt="User picture"
				class="w-full h-48 object-cover rounded-lg shadow-md transition-transform transform hover:scale-105"
			/>
		</div>
	</div>
</ng-template>
