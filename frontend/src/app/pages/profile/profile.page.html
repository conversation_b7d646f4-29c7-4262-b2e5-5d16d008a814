<!-- Show Complete Profile Component if needed -->
<ng-container *ngIf="!isMyProfileCompleted && isMyProfile; else completed">
	<component-complete-profile (profileCompleted)="profileCompleted()" />
</ng-container>

<!-- Profile Section -->
<ng-template #completed>
	<h1 class="text-4xl font-extrabold mb-10 text-center text-gray-900">
		<ng-container *ngIf="isMyProfile; else notMyProfile"
			>My Profile</ng-container
		>
		<ng-template #notMyProfile>
			{{ profileUser?.first_name }} {{ profileUser?.last_name }}'s Profile
		</ng-template>
	</h1>

	<!-- Profile Statistics -->
	<div class="max-w-4xl mx-auto mb-8">
		<!-- Fame Rating -->
		<div
			class="bg-gradient-to-r from-purple-600 to-orange-500 rounded-xl p-6 text-white text-center mb-6"
		>
			<h2 class="text-2xl font-bold mb-2">Fame Rating</h2>
			<div class="text-5xl font-extrabold">{{ fameRating.toFixed(0) }}</div>
			<div class="text-sm opacity-90 mt-2">
				<span *ngIf="fameRating >= 80">🔥 Popular</span>
				<span *ngIf="fameRating >= 60 && fameRating < 80">⭐ Well-liked</span>
				<span *ngIf="fameRating >= 40 && fameRating < 60">📈 Growing</span>
				<span *ngIf="fameRating < 40">🌱 New</span>
			</div>
		</div>

		<!-- Statistics Grid -->
		<div class="grid grid-cols-1 md:grid-cols-3 gap-4">
			<div class="bg-white rounded-lg p-4 shadow border text-center">
				<div class="text-2xl font-bold text-red-500">{{ likes.length }}</div>
				<div class="text-gray-600">Followers</div>
				<div class="text-xs text-gray-500">People who liked you</div>
			</div>
			<div class="bg-white rounded-lg p-4 shadow border text-center">
				<div class="text-2xl font-bold text-blue-500">
					{{ following.length }}
				</div>
				<div class="text-gray-600">Following</div>
				<div class="text-xs text-gray-500">People you liked</div>
			</div>
			<div class="bg-white rounded-lg p-4 shadow border text-center">
				<div class="text-2xl font-bold text-green-500">{{ views.length }}</div>
				<div class="text-gray-600">Profile Views</div>
				<div class="text-xs text-gray-500">Total views</div>
			</div>
		</div>
	</div>

	<div
		*ngIf="profileUser"
		class="max-w-5xl mx-auto p-6 sm:p-10 bg-white rounded-3xl shadow-xl border border-gray-100 space-y-10 relative"
	>
		<!-- Like Button -->
		<button
			*ngIf="!isMyProfile"
			(click)="toggleLike()"
			class="absolute top-5 right-5 px-4 py-1 text-sm font-semibold text-white transition rounded-full shadow"
			[ngClass]="isProfileLiked ? 'bg-red-600 hover:bg-red-500' : 'bg-purple-600 hover:bg-orange-500'"
		>
			<div *ngIf="!isProfileLiked">❤️ LIKE</div>
			<div *ngIf="isProfileLiked">❤️ UNLIKE</div>
		</button>
		<a
			*ngIf="isMyProfile"
			routerLink="/profile/edit"
			class="absolute top-5 right-5 px-4 py-1 text-sm font-semibold text-white bg-purple-600 hover:bg-orange-500 transition rounded-full shadow"
		>
			⚙️ Edit Profile
		</a>

		<!-- Profile Picture -->
		<div class="flex justify-center">
			<img
				[src]="getProfilePictureUrl(profileUser!.id)"
				alt="Profile picture"
				class="w-40 h-40 rounded-full object-cover border-4 border-white shadow-md ring-2 ring-purple-500 transition hover:scale-105 duration-200"
				onerror="this.onerror=null; this.src='/images/default-profile.png';"
			/>
		</div>

		<!-- User Info -->
		<div class="grid md:grid-cols-2 gap-4 text-gray-700 text-base">
			<p class="font-semibold text-lg text-gray-900">
				{{ profileUser!.first_name }} {{ profileUser!.last_name }} ({{
				profileUser!.username }})
			</p>
			<p>
				{{ profileUser!.birthdate | date: 'longDate' }} ({{ age }} years old)
			</p>
			<p>
				<span class="font-semibold">Gender:</span> {{ profileUser!.gender || '—'
				}}
			</p>
			<p>
				<span class="font-semibold">Sexual Preference:</span>
				{{ profileUser!.sexual_preference || '—' }}
			</p>
			<p>
				<span class="font-semibold">Status: </span>
				<ng-container *ngIf="isOnline(); else offline">
					<span class="text-green-500">Online</span>
				</ng-container>
				<ng-template #offline>
					<span class="text-red-500">Offline</span>
					<br />
					<span class="font-semibold">Last connection:</span>
					{{ profileUser!.last_connection | date: 'medium' }}
				</ng-template>
			</p>
			<p>
				<span class="font-semibold">Created At:</span>
				{{ profileUser!.created_at | date: 'medium' }}
			</p>
		</div>

		<!-- Biography & Interests -->
		<div *ngIf="profileUser!.biography" class="text-gray-700 space-y-4">
			<h2 class="text-xl font-bold text-gray-900">📝 Biography</h2>
			<p
				class="bg-gray-50 p-4 rounded-xl border border-gray-200 leading-relaxed"
			>
				{{ profileUser!.biography }}
			</p>

			<div *ngIf="profileUser!.interests?.length" class="pt-2">
				<h3 class="text-lg font-semibold text-gray-800 mb-2">🎯 Interests</h3>
				<div class="flex flex-wrap gap-2">
					<span
						*ngFor="let interest of profileUser!.interests"
						class="inline-block bg-indigo-100 text-indigo-800 px-3 py-1 rounded-full text-sm font-medium"
					>
						{{ interest }}
					</span>
				</div>
			</div>
		</div>

		<!-- Location Section -->
		<div *ngIf="mapUrl" class="space-y-4">
			<h2 class="text-xl font-bold text-gray-900">📍 Location</h2>
			<div class="rounded-xl overflow-hidden shadow border border-gray-200">
				<iframe
					[src]="mapUrl"
					width="100%"
					height="300"
					style="border: 0"
					allowfullscreen
					loading="lazy"
					referrerpolicy="no-referrer-when-downgrade"
				></iframe>
			</div>
			<p class="text-center text-gray-600">
				{{ profileUser!.city }}, {{ profileUser!.country }}
			</p>
			<p class="text-center text-sm text-gray-500">
				Longitude: {{ profileUser!.longitude }} | Latitude: {{
				profileUser!.latitude }}
			</p>
		</div>
	</div>

	<!-- User Pictures Section -->
	<div
		*ngIf="pictureUrls && pictureUrls.length > 0"
		class="max-w-5xl mx-auto p-6 sm:p-10 bg-white rounded-3xl shadow-xl border border-gray-100 mt-10 flex justify-center"
	>
		<div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
			<img
				*ngFor="let url of pictureUrls"
				[src]="url"
				alt="User picture"
				class="w-full h-48 object-cover rounded-lg shadow-md transition-transform transform hover:scale-105"
			/>
		</div>
	</div>
</ng-template>
