import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { UsersService } from '../../services/users.service';
import { User } from '../../models/user.model';
import { AuthService } from '../../services/auth.service';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { CompleteProfileComponent } from './complete-profile/complete-profile.component';
import { environment } from '../../../environments/environment';

@Component({
	selector: 'page-profile',
	templateUrl: './profile.page.html',
	imports: [CommonModule, CompleteProfileComponent, RouterLink],
})
export class ProfilePage {
	constructor(
		private _userService: UsersService,
		private _authService: AuthService,
		private sanitizer: DomSanitizer,
		private route: ActivatedRoute,
		private router: Router
	) {}

	public age: number = 0;
	public isMyProfile: boolean = false;
	public isMyProfileCompleted: boolean = false;
	public pictureUrls: string[] = [];
	public user: User | null = null;
	public mapUrl: SafeResourceUrl | null = null;

	private isProfileCompleted(user: User | null) {
		if (!user) return;

		if (
			user.gender &&
			user.interests &&
			user.interests.length > 0 &&
			user.sexual_preference &&
			user.sexual_preference.length > 0 &&
			user.biography
		)
			this.isMyProfileCompleted = true;
	}

	public getProfilePictureUrl(userId: number): string {
		return `${environment.apiUrl}/users/${userId}/profile-picture`;
	}

	private handleUserPictures(id: number) {
		this._userService.getUserPictureIds(id).subscribe({
			next: (response) => {
				const pictureIds = response.pictureIds;
				this.pictureUrls = pictureIds.map(
					(pictureId) => `${environment.apiUrl}/users/pictures/${pictureId}`
				);
			},
			error: (err) => {
				console.error('Error loading picture IDs', err);
			},
		});
	}

	private handleUser(user: User | null) {
		if (!user) {
			console.error('User not found');
			return;
		}

		this.user = user;
		this.age =
			new Date().getFullYear() - new Date(user.birthdate).getFullYear();

		if (this.user.sexual_preference) {
			this.user.sexual_preference = this.user.sexual_preference
				.replace(/{|}/g, '')
				.replace(/,/g, ', ');
		}
		this.generateMapUrl();
	}

	private generateMapUrl() {
		if (this.user && this.user.city && this.user.country) {
			const location = `${this.user.city}, ${this.user.country}`;
			const mapUrl = `https://maps.google.com/maps?q=${encodeURIComponent(
				location
			)}&output=embed`;
			this.mapUrl = this.sanitizer.bypassSecurityTrustResourceUrl(mapUrl);
		} else {
			this.mapUrl = null;
		}
	}

	public profileCompleted() {
		this.isMyProfileCompleted = true;
		this.fetchProfile();
	}

	public fetchProfile() {
		this.route.queryParams.subscribe((params) => {
			const userId = params['id'];
			const currentUser = this._authService.user();

			if (userId && userId != currentUser?.id) {
				// Fetch user by ID from query param
				this._userService.profile(userId).subscribe({
					next: (user) => {
						this.handleUser(user);
						this.handleUserPictures(user!.id);
					},
					error: (err) => {
						console.error('Failed to fetch user by ID:', err);
						this.router.navigate(['/profile']);
					},
				});
			} else {
				// Fetch authenticated user profile
				this.isMyProfile = true;
				if (currentUser && currentUser.id) {
					this._userService.profile(currentUser.id).subscribe((user) => {
						this.handleUser(user);
						this.isProfileCompleted(user);
						if (this.isMyProfileCompleted) {
							this.handleUserPictures(user!.id);
						}
					});
				} else {
					console.error('Authenticated user not found');
				}
			}
		});
	}

	ngOnInit() {
		this.fetchProfile();
	}
}
