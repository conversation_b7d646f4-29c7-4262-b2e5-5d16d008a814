import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { UsersService } from '../../services/users.service';
import { User } from '../../models/user.model';
import { AuthService } from '../../services/auth.service';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { CompleteProfileComponent } from './complete-profile/complete-profile.component';
import { environment } from '../../../environments/environment';
import { LikeService } from '../../services/like.service';
import { ViewService } from '../../services/view.service';

@Component({
	selector: 'page-profile',
	templateUrl: './profile.page.html',
	imports: [CommonModule, CompleteProfileComponent, RouterLink],
})
export class ProfilePage {
	constructor(
		private _userService: UsersService,
		private _authService: AuthService,
		private _likeService: LikeService,
		private _viewService: ViewService,
		private sanitizer: DomSanitizer,
		private route: ActivatedRoute,
		private router: Router
	) {}

	public me: User | null = null;
	public profileUser: User | null = null;

	public age: number = 0;
	public isMyProfile: boolean = false;
	public isMyProfileCompleted: boolean = false;
	public pictureUrls: string[] = [];
	public isProfileLiked: boolean = false;
	public mapUrl: SafeResourceUrl | null = null;

	public views: User[] = [];
	public likes: User[] = [];
	public following: User[] = [];
	public fameRating: number = 0.0;

	private _fetchLikes(): Promise<void> {
		return new Promise((resolve, reject) => {
			this._likeService.getLikesById(this.profileUser!.id).subscribe({
				next: (likes: User[]) => {
					this.likes = likes;
					resolve();
				},
				error: (err) => {
					console.error('Error fetching likes', err);
					reject(err);
				},
			});
		});
	}

	private _fetchViews(): Promise<void> {
		return new Promise((resolve, reject) => {
			this._viewService.getViewsById(this.profileUser!.id).subscribe({
				next: (views: User[]) => {
					this.views = views;
					resolve();
				},
				error: (err) => {
					console.error('Error fetching views', err);
					reject(err);
				},
			});
		});
	}

	private _fetchFollowing(): Promise<void> {
		return new Promise((resolve, reject) => {
			this._likeService.getFollowing(this.profileUser!.id).subscribe({
				next: (following: User[]) => {
					this.following = following;
					resolve();
				},
				error: (err: any) => {
					console.error('Error fetching following', err);
					reject(err);
				},
			});
		});
	}

	private async _fetchFameRating() {
		await this._fetchViews();
		await this._fetchLikes();
		await this._fetchFollowing();

		const viewCount = this.views.length || 1;
		const likeCount = this.likes.length;
		const followingCount = this.following.length;

		// Enhanced fame rating: base + activity bonus
		const baseRating = (likeCount / viewCount) * 100;
		const activityBonus = Math.min(followingCount * 2, 20);
		this.fameRating = Math.min(baseRating + activityBonus, 100);
	}

	private isProfileCompleted() {
		if (!this.profileUser) return;

		if (
			this.profileUser.gender &&
			this.profileUser.interests &&
			this.profileUser.interests.length > 0 &&
			this.profileUser.sexual_preference &&
			this.profileUser.sexual_preference.length > 0 &&
			this.profileUser.biography
		)
			this.isMyProfileCompleted = true;
	}

	public getProfilePictureUrl(userId: number): string {
		return `${environment.apiUrl}/users/${userId}/profile-picture`;
	}

	private handleUserPictures() {
		this._userService.getUserPictureIds(this.profileUser!.id).subscribe({
			next: (response) => {
				const pictureIds = response.pictureIds;
				this.pictureUrls = pictureIds.map(
					(pictureId) => `${environment.apiUrl}/users/pictures/${pictureId}`
				);
			},
			error: (err) => {
				console.error('Error loading picture IDs', err);
			},
		});
	}

	private _isProfileLiked(): void {
		if (!this.profileUser) return;

		this._likeService.isLiked(this.profileUser.id).subscribe({
			next: (response) => {
				this.isProfileLiked = response.liked;
			},
			error: (err) => {
				console.error('Error checking if profile is liked', err);
			},
		});
	}

	public toggleLike() {
		if (!this.profileUser) return;

		this._likeService.toggleLike(this.profileUser.id).subscribe({
			next: () => {
				this._isProfileLiked();
				this._fetchFameRating();
			},
			error: (err) => {
				console.error('Error toggling profile like status', err);
			},
		});
	}

	private _createView(): void {
		if (!this.profileUser) return;

		this._viewService.createView(this.profileUser.id).subscribe({
			next: () => {},
			error: (err) => {
				console.error('Error creating view', err);
			},
		});
	}

	private handleUser() {
		this.age =
			new Date().getFullYear() -
			new Date(this.profileUser!.birthdate).getFullYear();

		if (this.profileUser!.sexual_preference) {
			this.profileUser!.sexual_preference =
				this.profileUser!.sexual_preference.replace(/{|}/g, '').replace(
					/,/g,
					', '
				);
		}
		this.generateMapUrl();
	}

	private generateMapUrl() {
		if (this.profileUser && this.profileUser.city && this.profileUser.country) {
			const location = `${this.profileUser.city}, ${this.profileUser.country}`;
			const mapUrl = `https://maps.google.com/maps?q=${encodeURIComponent(
				location
			)}&output=embed`;
			this.mapUrl = this.sanitizer.bypassSecurityTrustResourceUrl(mapUrl);
		} else {
			this.mapUrl = null;
		}
	}

	public profileCompleted() {
		this.isMyProfileCompleted = true;
		this.fetchProfile();
	}

	public isOnline(): boolean {
		if (!this.profileUser || !this.profileUser.last_connection) return false;

		const lastConnection = new Date(this.profileUser.last_connection);
		const now = new Date();
		const diffInMinutes = Math.floor(
			(now.getTime() - lastConnection.getTime()) / 60000
		);

		return diffInMinutes < 5; // Online if last connection was within the last 5 minutes
	}

	public fetchProfile() {
		this.route.queryParams.subscribe((params) => {
			const userId = params['id'];
			this.me = this._authService.user();

			if (userId && userId != this.me?.id) {
				// Fetch user by ID from query param
				this._userService.profile(userId).subscribe({
					next: (user) => {
						this.profileUser = user;
						this.handleUser();
						this.handleUserPictures();
						this._isProfileLiked();
						this._fetchFameRating();
						this._createView();
					},
					error: (err) => {
						console.error('Failed to fetch user by ID:', err);
						this.router.navigate(['/profile']);
					},
				});
			} else {
				// Fetch authenticated user profile
				this.isMyProfile = true;
				if (this.me && this.me.id) {
					this._userService.profile(this.me.id).subscribe((user) => {
						this.profileUser = user;
						this.handleUser();
						this._fetchFameRating();
						this.isProfileCompleted();
						if (this.isMyProfileCompleted) {
							this.handleUserPictures();
						}
					});
				} else {
					console.error('Authenticated user not found');
				}
			}
		});
	}

	public ngOnInit() {
		this.fetchProfile();
	}
}
