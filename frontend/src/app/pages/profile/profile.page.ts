import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { UsersService } from '../../services/users.service';
import { User } from '../../models/user.model';
import { AuthService } from '../../services/auth.service';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
import { ActivatedRoute, Router } from '@angular/router';

@Component({
	selector: 'page-profile',
	templateUrl: './profile.page.html',
	imports: [CommonModule],
})
export class ProfilePage {
	constructor(
		private _userService: UsersService,
		private _authService: AuthService,
		private sanitizer: DomSanitizer,
		private route: ActivatedRoute,
		private router: Router
	) {}

	public isMyProfile: boolean = false;
	public user: User | null = null;
	public mapUrl: SafeResourceUrl | null = null;

	private handleUser(user: User | null) {
		if (!user) {
			console.error('User not found');
			return;
		}

		this.user = user;

		if (this.user.sexual_preference) {
			this.user.sexual_preference = this.user.sexual_preference
				.replace(/{|}/g, '')
				.replace(/,/g, ', ');
		}

		if (
			this.user.location &&
			this.user.location.x !== null &&
			this.user.location.y !== null
		) {
			const url = `https://maps.google.com/maps?q=${this.user.location.y},${this.user.location.x}&z=14&output=embed`;
			this.mapUrl = this.sanitizer.bypassSecurityTrustResourceUrl(url);
		}
	}

	ngOnInit() {
		this.route.queryParams.subscribe((params) => {
			const userId = params['id'];
			const currentUser = this._authService.user();

			if (userId && userId != currentUser?.id) {
				// Fetch user by ID from query param
				this._userService.profile(userId).subscribe({
					next: (user) => {
						this.handleUser(user);
					},
					error: (err) => {
						console.error('Failed to fetch user by ID:', err);
						this.router.navigate(['/profile']);
					},
				});
			} else {
				// Fetch authenticated user profile
				this.isMyProfile = true;
				if (currentUser && currentUser.id) {
					this._userService.profile(currentUser.id).subscribe((user) => {
						this.handleUser(user);
					});
				} else {
					console.error('Authenticated user not found');
				}
			}
		});
	}
}
