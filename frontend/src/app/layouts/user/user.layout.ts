import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AppComponent } from '../../app.component';
import { FooterComponent } from '../../components/footer/footer.component';
import { NavbarComponent } from '../../components/navbar/navbar.component';
import { NotificationComponent } from '../../components/notification/notification.component';

@Component({
	selector: 'layout-user',
	templateUrl: './user.layout.html',
	standalone: true,
	imports: [
		CommonModule,
		AppComponent,
		FooterComponent,
		NavbarComponent,
		NotificationComponent,
	],
})
export class UserLayout {}
