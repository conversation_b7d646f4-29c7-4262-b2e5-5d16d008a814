import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
	name: 'age',
})
export class AgePipe implements PipeTransform {
	transform(birthdate: string | Date): number {
		if (!birthdate) return 0;
		const birth = new Date(birthdate);
		const today = new Date();
		let age = today.getFullYear() - birth.getFullYear();
		const m = today.getMonth() - birth.getMonth();
		if (m < 0 || (m === 0 && today.getDate() < birth.getDate())) {
			age--;
		}
		return age;
	}
}
