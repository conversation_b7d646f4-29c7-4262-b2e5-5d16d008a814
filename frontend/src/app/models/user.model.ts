export interface User {
	id: number;
	first_name: string;
	last_name: string;
	username: string;
	password: string;
	email: string;
	email_verification_token: string | null;
	email_verified: boolean;
	password_reset_token: string | null;
	location: {
		x: number | null;
		y: number | null;
	};
	gender: string | null;
	sexual_preference: string | null;
	biography: string | null;
	interests: string[] | null;
	created_at: Date;
}
