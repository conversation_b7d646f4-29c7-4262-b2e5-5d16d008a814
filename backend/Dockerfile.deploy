# Dockerfile for deploying the backend application
FROM node:18-alpine AS builder

WORKDIR /app

COPY package.json package-lock.json ./
RUN npm install

COPY tsconfig.json ./
COPY src ./src

RUN npm run build

# Final stage for production deployment
FROM node:18-alpine

WORKDIR /app

COPY package.json package-lock.json ./
RUN npm install --production

COPY --from=builder /app/dist ./dist

RUN addgroup -S appgroup && adduser -S appuser -G appgroup
USER appuser

EXPOSE 3000

ENV ENV=dev
ENV NODE_ENV=production
ENV BACKEND_URL=http://matcha.mvpee.be
ENV FRONTEND_URL=http://matcha.mvpee.be

CMD ["node", "dist/server.js"]
