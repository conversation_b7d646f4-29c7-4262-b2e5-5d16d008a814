import { createLogger, format, transports } from 'winston';

const customTimestampFormat = format((info) => {
	const now = new Date();
	const MM = String(now.getMonth() + 1).padStart(2, '0');
	const DD = String(now.getDate()).padStart(2, '0');
	const YYYY = now.getFullYear();
	const HH = String(now.getHours()).padStart(2, '0');
	const mm = String(now.getMinutes()).padStart(2, '0');
	const ss = String(now.getSeconds()).padStart(2, '0');

	info.timestamp = `${MM}-${DD}-${YYYY} ${HH}:${mm}:${ss}`;
	return info;
});

const levelColor = (level: string): string => {
	switch (level) {
		case 'info':
			return '\x1b[32m'; // green
		case 'warn':
			return '\x1b[33m'; // yellow
		case 'error':
			return '\x1b[31m'; // red
		case 'debug':
			return '\x1b[34m'; // blue
		default:
			return '\x1b[0m'; // reset
	}
};

const logger = createLogger({
	level: 'debug',
	format: format.combine(
		customTimestampFormat(),
		format.printf(({ level, message, timestamp }) => {
			const color = levelColor(level);
			const reset = '\x1b[0m';
			return `${timestamp} ${color}[${level.toUpperCase()}]${reset}: ${message}`;
		})
	),
	transports: [new transports.Console()],
});

export default logger;
