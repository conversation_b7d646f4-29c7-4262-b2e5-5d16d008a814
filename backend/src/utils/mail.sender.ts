import nodemailer from 'nodemailer';
import logger from './logger';

const transporter = nodemailer.createTransport({
	host: process.env.SMTP_EMAIL_HOST,
	port: Number(process.env.SMTP_EMAIL_PORT),
	secure: Number(process.env.SMTP_EMAIL_PORT) === 465,
	auth: {
		user: process.env.SMTP_EMAIL_USER,
		pass: process.env.SMTP_EMAIL_PASSWORD,
	},
});

export const sendEmail = async (
	to: string,
	subject: string,
	html: string
): Promise<void> => {
	try {
		await transporter.sendMail({
			from: `"Matcha" <${process.env.SMTP_EMAIL_USER}>`,
			to,
			subject,
			html,
		});
		logger.info(`[MAILER] Email sent to ${to}`);
	} catch (error) {
		logger.error('[MAILER] Failed to send email:', error);
		throw error;
	}
};
