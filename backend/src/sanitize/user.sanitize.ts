import { User } from '../model/user.model';

export type SafeUser = Omit<
	User,
	| 'password'
	| 'email_verification_token'
	| 'email_verified'
	| 'password_reset_token'
	| 'email'
>;

export const sanitizeUser = (user: User): SafeUser => {
	const {
		password,
		email_verification_token,
		email_verified,
		password_reset_token,
		email,
		...safeUser
	} = user;
	return safeUser;
};

export const sanitizeUsers = (users: User[]): SafeUser[] => {
	return users.map(sanitizeUser);
};
