import express from 'express';
import http from 'http';
import logger from './utils/logger';
import corsOptions from './config/cors';
import AuthRouter from './api/auth.route';
import UserRouter from './api/users.route';
import LikeRouter from './api/like.route';
import ViewRouter from './api/view.route';
import BlockRouter from './api/block.route';
import ReportRouter from './api/report.route';
import MessageRouter from './api/message.route';
import NotificationRouter from './api/notification.route';
import cors from 'cors';
import db from './config/db';
import swaggerUi from 'swagger-ui-express';
import swaggerSpec from './config/swagger';
import { chatWss } from './websocket/chat.websocket';
import { notificationWss } from './websocket/notification.websocket';

const app = express();
const server = http.createServer(app);
const port = 3000;

db.connect()
	.then(() => logger.info('Database connected successfully'))
	.catch((err) => logger.error('Database connection error:', err));

app.set('trust proxy', true);
app.use(cors(corsOptions));
app.use(express.json());

app.use('/api/auth', AuthRouter);
app.use('/api/users', UserRouter);
app.use('/api/like', LikeRouter);
app.use('/api/view', ViewRouter);
app.use('/api/message', MessageRouter);
app.use('/api/notification', NotificationRouter);
app.use('/api/report', ReportRouter);
app.use('/api/block', BlockRouter);

server.on('upgrade', (request, socket, head) => {
	const path = request.url?.split('?')[0];

	if (path === '/ws/chat') {
		chatWss.handleUpgrade(request, socket, head, (ws) => {
			chatWss.emit('connection', ws, request);
		});
	} else if (path === '/ws/notification') {
		notificationWss.handleUpgrade(request, socket, head, (ws) => {
			notificationWss.emit('connection', ws, request);
		});
	} else {
		socket.destroy();
	}
});

if (process.env.NODE_ENV !== 'production') {
	app.use('/', swaggerUi.serve, swaggerUi.setup(swaggerSpec));
}

server.listen(port, () => {
	logger.info(`Matcha backend is running on port ${port}`);
});
