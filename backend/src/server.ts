import express from 'express';
import logger from './utils/logger';
import corsOptions from './config/cors';
import AuthRouter from './api/auth/auth.route';
import UserRouter from './api/user/users.route';
import cors from 'cors';
import db from './config/db';
import swaggerUi from 'swagger-ui-express';
import swaggerSpec from './config/swagger';

const app = express();
const port = 3000;

db.connect()
	.then(() => logger.info('Database connected successfully'))
	.catch((err) => logger.error('Database connection error:', err));

app.set('trust proxy', true);
app.use(cors(corsOptions));
app.use(express.json());

app.use('/api/auth', AuthRouter);
app.use('/api/users', UserRouter);

if (process.env.NODE_ENV !== 'production') {
	app.use('/', swaggerUi.serve, swaggerUi.setup(swaggerSpec));
}

app.listen(port, () => {
	logger.info(`Matcha backend is running on port ${port}`);
});
