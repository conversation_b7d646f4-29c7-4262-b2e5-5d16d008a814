import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { User } from '../../model/user.model';
import { UserService } from '../../service/user.service';

const JWT_SECRET = process.env.JWT_SECRET || 'secret';

interface JwtPayloadExtended {
	id: number;
	username: string;
	email: string;
}

export class AuthMiddleware {
	private userService = new UserService();

	authenticateToken(req: Request, res: Response, next: NextFunction) {
		const authHeader = req.headers['authorization'];
		const token = authHeader?.split(' ')[1];

		if (!token) return res.status(401).json({ error: 'Missing token' });

		try {
			const decoded = jwt.verify(token, JWT_SECRET) as JwtPayloadExtended;

			(req as Request & { user?: JwtPayloadExtended }).user = decoded;

			next();
		} catch {
			res.status(403).json({ error: 'Invalid token' });
		}
	}

	async getAuthenticatedUser(req: Request): Promise<User | null> {
		const authHeader = req.headers['authorization'];
		const token = authHeader?.split(' ')[1];

		if (!token) return Promise.resolve(null);

		try {
			const decode = jwt.verify(token, JWT_SECRET) as JwtPayloadExtended;
			return this.userService.getUserById(decode.id);
		} catch {
			return Promise.resolve(null);
		}
	}

	async getAuthenticatedUserByToken(token: string): Promise<User | null> {
		if (!token) return Promise.resolve(null);

		try {
			const decode = jwt.verify(token, JWT_SECRET) as JwtPayloadExtended;
			return this.userService.getUserById(decode.id);
		} catch {
			return Promise.resolve(null);
		}
	}
}
