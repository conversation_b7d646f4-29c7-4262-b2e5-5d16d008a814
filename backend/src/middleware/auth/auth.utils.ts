import { IncomingMessage } from 'http';
import jwt from 'jsonwebtoken';
import axios from 'axios';

const JWT_SECRET: string = process.env.JWT_SECRET || 'secret';

export function generateToken(payload: object): string {
	return jwt.sign(payload, JWT_SECRET, {
		expiresIn: process.env.JWT_EXPIRATION || '1h',
	} as jwt.SignOptions);
}

export function verifyToken(token: string): any {
	return jwt.verify(token, JWT_SECRET);
}

export function getClientIp(req: IncomingMessage): string {
	let ip = req.headers['x-forwarded-for'] || req.socket.remoteAddress;
	if (Array.isArray(ip)) ip = ip[0];
	return (
		ip
			?.toString()
			.replace(/^::ffff:/, '')
			.split(',')[0]
			.trim() || ''
	);
}

export async function geolocateIp(ip: string) {
	try {
		const { data } = await axios.get(`http://ip-api.com/json/${ip}`, {
			params: { fields: 'country,city,lat,lon' },
		});

		if (data.status === 'success') {
			return {
				country: data.country,
				city: data.city,
				lat: data.lat,
				lon: data.lon,
			};
		} else {
			throw new Error(data.message);
		}
	} catch (err) {
		return null;
	}
}
