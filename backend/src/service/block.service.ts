import pool from '../config/db';
import { LikeService } from './like.service';

const likeService = new LikeService();

export class BlockService {
	public async blockUser(fromUserId: number, toUserId: number): Promise<void> {
		if (await likeService.isLiked(fromUserId, toUserId)) {
			await likeService.removeLike(fromUserId, toUserId);
		}

		const query = `
            INSERT INTO blocks (blocker_id, blocked_id)
            VALUES ($1, $2)
        `;
		await pool.query(query, [fromUserId, toUserId]);
	}

	public async unblockUser(
		fromUserId: number,
		toUserId: number
	): Promise<void> {
		const query = `
            DELETE FROM blocks
            WHERE blocker_id = $1 AND blocked_id = $2
        `;
		await pool.query(query, [fromUserId, toUserId]);
	}

	public async isBlocked(
		fromUserId: number,
		toUserId: number
	): Promise<boolean> {
		const query = `
            SELECT EXISTS (
                SELECT 1
                FROM blocks
                WHERE blocker_id = $1 AND blocked_id = $2
            ) AS is_blocked
        `;
		const result = await pool.query(query, [fromUserId, toUserId]);
		return result.rows[0].is_blocked;
	}

	public async toggleBlock(
		fromUserId: number,
		toUserId: number
	): Promise<void> {
		const isBlocked = await this.isBlocked(fromUserId, toUserId);

		if (isBlocked) {
			await this.unblockUser(fromUserId, toUserId);
		} else {
			await this.blockUser(fromUserId, toUserId);
		}
	}
}
