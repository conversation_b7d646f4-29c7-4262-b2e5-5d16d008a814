import pool from '../config/db';
import { User } from '../model/user.model';
import crypto from 'crypto';
import { sendEmail } from '../utils/mail.sender';
import { CompleteProfile } from '../model/request/complete-profile.model';
import logger from '../utils/logger';
import { UserPicture } from '../model/user-picture.model';

export class UserService {
	public async getUserById(id: number): Promise<User | null> {
		const query = 'SELECT * FROM users WHERE id = $1';
		const result = await pool.query(query, [id]);
		return result.rows[0] || null;
	}

	public async getUserByUsername(username: string): Promise<User | null> {
		const query = 'SELECT * FROM users WHERE username = $1';
		const result = await pool.query(query, [username]);
		return result.rows[0] || null;
	}

	public async getUserByEmail(email: string): Promise<User | null> {
		const query = 'SELECT * FROM users WHERE email = $1';
		const result = await pool.query(query, [email]);
		return result.rows[0] || null;
	}

	public async getSuggestedMatches(
		currentUser: User,
		options: {
			maxDistanceKm?: number;
			limit?: number;
			offset?: number;
			ageGap?: number;
		} = {}
	): Promise<User[]> {
		const { id, gender, sexual_preference, latitude, longitude, interests } =
			currentUser;

		const ageGap = options.ageGap ?? 5;
		const now = new Date();
		const currentUserAge = currentUser.birthdate
			? Math.floor(
					(now.getTime() - new Date(currentUser.birthdate).getTime()) /
						(1000 * 3600 * 24 * 365.25)
			  )
			: null;

		let minBirthdate = null;
		let maxBirthdate = null;

		if (currentUserAge !== null) {
			const minAge = currentUserAge - ageGap;
			const maxAge = currentUserAge + ageGap;
			minBirthdate = new Date(
				now.getFullYear() - maxAge,
				now.getMonth(),
				now.getDate()
			);
			maxBirthdate = new Date(
				now.getFullYear() - minAge,
				now.getMonth(),
				now.getDate()
			);
		}

		const maxDistance = options.maxDistanceKm ?? 50;
		const limit = options.limit ?? 20;
		const offset = options.offset ?? 0;

		// SQL query to find suggested matches
		// Using Haversine formula to calculate distance between two points on the Earth (latitude, longitude) => 6371 * acos(cos(radians(lat1)) * cos(radians(lat2)) * cos(radians(lon2) - radians(lon1)) + sin(radians(lat1)) * sin(radians(lat2)))
		const query = `
			WITH filtered_users AS (
				SELECT *,

					(
						6371 * acos(
							cos(radians($1)) *
							cos(radians(latitude)) *
							cos(radians(longitude) - radians($2)) +
							sin(radians($1)) *
							sin(radians(latitude))
						)
					) AS distance,

					cardinality(ARRAY(
						SELECT UNNEST($3::varchar[])
						INTERSECT
						SELECT UNNEST(interests)
					)) AS common_tags

				FROM users
				WHERE id != $4
					AND gender = ANY($5)
					AND $6 = ANY(sexual_preference)
					AND ($10::date IS NULL OR birthdate BETWEEN $10 AND $11)
			)

			SELECT * FROM filtered_users
			WHERE distance <= $7
			ORDER BY distance ASC, common_tags DESC, created_at DESC
			LIMIT $8 OFFSET $9
		`;

		const values = [
			latitude, // $1
			longitude, // $2
			interests || [], // $3
			id, // $4
			sexual_preference, // $5
			gender, // $6
			maxDistance, // $7
			limit, // $8
			offset, // $9
			minBirthdate, // $10
			maxBirthdate, // $11
		];

		const result = await pool.query(query, values);
		return result.rows;
	}

	public async getUserProfilePicture(
		userId: number
	): Promise<UserPicture | null> {
		const query = `
			SELECT * FROM user_pictures
			WHERE user_id = $1 AND profile_picture = TRUE
			LIMIT 1
		`;
		const result = await pool.query(query, [userId]);
		return result.rows[0] || null;
	}

	public async getUsersPicturesId(userId: number): Promise<UserPicture[]> {
		const query = `
			SELECT id FROM user_pictures
			WHERE user_id = $1 AND profile_picture = FALSE
			ORDER BY created_at DESC
		`;
		const result = await pool.query(query, [userId]);
		return result.rows;
	}

	public async getPictureById(pictureId: number): Promise<UserPicture | null> {
		const query = 'SELECT * FROM user_pictures WHERE id = $1';
		const result = await pool.query(query, [pictureId]);
		return result.rows[0] || null;
	}

	public async addUserPicture(
		userId: number,
		pictureBuffer: Buffer,
		pictureContentType: string,
		profile_picture: boolean = false
	): Promise<void> {
		const query = `
			INSERT INTO user_pictures (user_id, profile_picture, data, content_type)
			VALUES ($1, $2, $3, $4)
		`;
		await pool.query(query, [
			userId,
			profile_picture,
			pictureBuffer,
			pictureContentType,
		]);
	}

	public async countUserPictures(userId: number): Promise<number> {
		const query = 'SELECT COUNT(*) FROM user_pictures WHERE user_id = $1';
		const result = await pool.query(query, [userId]);
		return parseInt(result.rows[0].count, 10);
	}

	public async deletePicture(
		pictureId: number,
		userId: number
	): Promise<boolean> {
		const query = 'DELETE FROM user_pictures WHERE id = $1 AND user_id = $2';
		const result = await pool.query(query, [pictureId, userId]);
		return (result.rowCount ?? 0) > 0;
	}

	public async setProfilePicture(
		pictureId: number,
		userId: number
	): Promise<boolean> {
		const client = await pool.connect();
		try {
			await client.query('BEGIN');

			// First, unset all profile pictures for this user
			await client.query(
				'UPDATE user_pictures SET profile_picture = FALSE WHERE user_id = $1',
				[userId]
			);

			// Then set the specified picture as profile picture
			const result = await client.query(
				'UPDATE user_pictures SET profile_picture = TRUE WHERE id = $1 AND user_id = $2',
				[pictureId, userId]
			);

			await client.query('COMMIT');
			return (result.rowCount ?? 0) > 0;
		} catch (error) {
			await client.query('ROLLBACK');
			throw error;
		} finally {
			client.release();
		}
	}

	public async addSingleUserPicture(
		userId: number,
		pictureBuffer: Buffer,
		pictureContentType: string
	): Promise<{ id: number }> {
		const query = `
			INSERT INTO user_pictures (user_id, profile_picture, data, content_type)
			VALUES ($1, FALSE, $2, $3)
			RETURNING id
		`;
		const result = await pool.query(query, [
			userId,
			pictureBuffer,
			pictureContentType,
		]);
		return { id: result.rows[0].id };
	}

	public async createUser(userData: {
		first_name: string;
		last_name: string;
		username: string;
		email: string;
		password: string;
	}): Promise<void> {
		const email_verification_token = crypto.randomBytes(64).toString('hex');

		const query = `
			INSERT INTO users (first_name, last_name, username, email, password, email_verification_token)
			VALUES ($1, $2, $3, $4, $5, $6)
		`;
		await pool.query(query, [
			userData.first_name,
			userData.last_name,
			userData.username,
			userData.email,
			userData.password,
			email_verification_token,
		]);

		sendEmail(
			userData.email,
			'Verify your Matcha account',
			`
				<p>Hi ${userData.first_name},</p>
				<p>Thank you for registering on Matcha! Please verify your account using the link below:</p>
				<p><a href="${process.env.BACKEND_URL}/api/auth/verify-email?token=${email_verification_token}">Verify Email</a></p>
				<p>Best regards,<br>Matcha Team</p>
			`
		);
	}

	public async updateUserPasswordResetToken(
		userId: number,
		password_reset_token: string | null
	): Promise<void> {
		const query = 'UPDATE users SET password_reset_token = $1 WHERE id = $2';
		await pool.query(query, [password_reset_token, userId]);
	}

	public async forgotPassword(email: string): Promise<void> {
		const user = await this.getUserByEmail(email);
		if (!user) {
			logger.info(
				`[WAF] Forgot password request for non-existent email: ${email}`
			);
			return;
		}

		const password_reset_token = crypto.randomBytes(64).toString('hex');

		await this.updateUserPasswordResetToken(user.id, password_reset_token);

		sendEmail(
			user.email,
			'Reset your Matcha password',
			`
				<p>Hi ${user.first_name},</p>
				<p>We received a request to reset your password. If you did not make this request, please ignore this email.</p>
				<p>To reset your password, please click the link below:</p>
				<p><a href="${process.env.FRONTEND_URL}/reset-password?token=${password_reset_token}">Reset Password</a></p>
				<p>Best regards,<br>Matcha Team</p>
			`
		);
		logger.info(`Password reset email sent to ${email}`);
	}

	public async updateUserPassword(
		id: number,
		newPassword: string
	): Promise<void> {
		const query = 'UPDATE users SET password = $1 WHERE id = $2';
		await pool.query(query, [newPassword, id]);
	}

	public async updateUserLocation(
		userId: number,
		location: {
			country: string;
			city: string;
			longitude: number;
			latitude: number;
		}
	): Promise<void> {
		const query = `
			UPDATE users
			SET country = $1, city = $2, longitude = $3, latitude = $4
			WHERE id = $5
		`;
		await pool.query(query, [
			location.country,
			location.city,
			location.longitude,
			location.latitude,
			userId,
		]);
	}

	public async isEmailVerified(email: string): Promise<boolean> {
		const query = 'SELECT email_verified FROM users WHERE email = $1';
		const result = await pool.query(query, [email]);
		if (result.rows.length === 0) {
			return false;
		}
		return result.rows[0].email_verified;
	}

	public async completeUserProfile(
		userId: number,
		profile: CompleteProfile
	): Promise<void> {
		const query = `
			UPDATE users
			SET gender = $1,
			sexual_preference = $2,
			biography = $3,
			interests = $4,
			birthdate = $5
			WHERE id = $6
		`;
		await pool.query(query, [
			profile.gender,
			profile.sexual_preference,
			profile.biography,
			profile.interests,
			profile.birthdate,
			userId,
		]);
	}

	public async updateUserBasicInfo(
		userId: number,
		userData: {
			first_name: string;
			last_name: string;
			email: string;
		}
	): Promise<void> {
		const query = `
			UPDATE users
			SET first_name = $1, last_name = $2, email = $3
			WHERE id = $4
		`;
		await pool.query(query, [
			userData.first_name,
			userData.last_name,
			userData.email,
			userId,
		]);
	}

	public async updateUserProfileInfo(
		userId: number,
		profileData: {
			gender?: string;
			sexual_preference?: string[];
			biography?: string;
			interests?: string[];
		}
	): Promise<void> {
		const fields = [];
		const values = [];
		let paramIndex = 1;

		if (profileData.gender !== undefined) {
			fields.push(`gender = $${paramIndex++}`);
			values.push(profileData.gender);
		}
		if (profileData.sexual_preference !== undefined) {
			fields.push(`sexual_preference = $${paramIndex++}`);
			values.push(profileData.sexual_preference);
		}
		if (profileData.biography !== undefined) {
			fields.push(`biography = $${paramIndex++}`);
			values.push(profileData.biography);
		}
		if (profileData.interests !== undefined) {
			fields.push(`interests = $${paramIndex++}`);
			values.push(profileData.interests);
		}

		if (fields.length === 0) return;

		values.push(userId);
		const query = `UPDATE users SET ${fields.join(
			', '
		)} WHERE id = $${paramIndex}`;
		await pool.query(query, values);
	}

	public async isEmailVerificationTokenValid(
		emailVerificationToken: string
	): Promise<boolean> {
		const query =
			'SELECT * FROM users WHERE email_verification_token = $1 AND email_verified = FALSE';
		const result = await pool.query(query, [emailVerificationToken]);
		return result.rows.length > 0;
	}

	public async updateEmailVerificationToken(
		emailVerificationToken: string
	): Promise<void> {
		const query = `
			UPDATE users
			SET email_verified = TRUE, email_verification_token = NULL
			WHERE email_verification_token = $1
		`;
		await pool.query(query, [emailVerificationToken]);
	}

	public async getAllUsers(): Promise<User[]> {
		const query = 'SELECT * FROM users';
		const result = await pool.query(query);
		return result.rows;
	}

	public async getUserByPasswordResetToken(
		passwordResetToken: string
	): Promise<User | null> {
		const query = 'SELECT * FROM users WHERE password_reset_token = $1';
		const result = await pool.query(query, [passwordResetToken]);
		return result.rows[0] || null;
	}

	public async updateLastConnection(userId: number): Promise<void> {
		const query = `
			UPDATE users
			SET last_connection = NOW()
			WHERE id = $1
		`;
		await pool.query(query, [userId]);
	}

	public async getLastConnectionInMinutes(userId: number): Promise<number> {
		const query = `
			SELECT EXTRACT(EPOCH FROM (NOW() - last_connection)) / 60 AS minutes
			FROM users
			WHERE id = $1
		`;
		const result = await pool.query(query, [userId]);
		if (result.rows.length === 0) {
			throw new Error(`User with ID ${userId} not found`);
		}
		return Math.floor(result.rows[0].minutes);
	}
}
