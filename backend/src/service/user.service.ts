import pool from '../config/db';
import { User } from '../model/user.model';

export const getUserById = async (id: number): Promise<User | null> => {
	const query = 'SELECT * FROM users WHERE id = $1';
	const result = await pool.query(query, [id]);
	return result.rows[0] || null;
};

export const getUserByUsername = async (
	username: string
): Promise<User | null> => {
	const query = 'SELECT * FROM users WHERE username = $1';
	const result = await pool.query(query, [username]);
	return result.rows[0] || null;
};

export const getUserByEmail = async (email: string): Promise<User | null> => {
	const query = 'SELECT * FROM users WHERE email = $1';
	const result = await pool.query(query, [email]);
	return result.rows[0] || null;
};

export const createUser = async (userData: {
	first_name: string;
	last_name: string;
	username: string;
	email: string;
	password: string;
}): Promise<void> => {
	const query = `
		INSERT INTO users (first_name, last_name, username, email, password)
		VALUES ($1, $2, $3, $4, $5)
	`;
	await pool.query(query, [
		userData.first_name,
		userData.last_name,
		userData.username,
		userData.email,
		userData.password,
	]);
};

export const updateUserPassword = async (
	username: string,
	newPassword: string
): Promise<void> => {
	const query = 'UPDATE users SET password = $1 WHERE username = $2';
	await pool.query(query, [newPassword, username]);
};
