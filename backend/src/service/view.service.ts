import pool from '../config/db';
import { User } from '../model/user.model';
import { View } from '../model/view.model';
import { sendNotification } from '../websocket/notification.websocket';

export class ViewService {
	public async getViews(userId: number): Promise<User[]> {
		const query = `
			SELECT DISTINCT ON (v.from_user_id) u.*, v.created_at
			FROM views v
			INNER JOIN users u ON u.id = v.from_user_id
			WHERE v.to_user_id = $1
			ORDER BY v.from_user_id, v.created_at DESC
		`;
		const result = await pool.query(query, [userId]);
		return result.rows;
	}

	public async createView(fromUser: User, toUserId: number): Promise<void> {
		const query = `
            INSERT INTO views (from_user_id, to_user_id)
            VALUES ($1, $2)
        `;
		await pool.query(query, [fromUser.id, toUserId]);
		sendNotification(toUserId, 'View', `${fromUser.username} viewed you!`);
	}

	public async getViewsByUserId(userId: number): Promise<View[]> {
		const query = `
            SELECT * FROM views
            WHERE to_user_id = $1
        `;
		const result = await pool.query(query, [userId]);
		return result.rows;
	}

	public async getViewCountByUserId(userId: number): Promise<number> {
		const query = `
        SELECT COUNT(*) AS view_count
        FROM views
        WHERE to_user_id = $1
    `;
		const result = await pool.query(query, [userId]);
		return parseInt(result.rows[0].view_count);
	}

	public async getViewCountByUserIdLastMonth(userId: number): Promise<number> {
		const query = `
        SELECT COUNT(*) AS view_count
        FROM views
        WHERE to_user_id = $1 AND created_at >= NOW() - INTERVAL '1 month'
    `;
		const result = await pool.query(query, [userId]);
		return parseInt(result.rows[0].view_count);
	}
}
