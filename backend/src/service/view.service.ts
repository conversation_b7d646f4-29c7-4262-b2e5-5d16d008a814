import pool from '../config/db';
import { View } from '../model/view.model';

export class ViewService {
	public async createView(fromUserId: number, toUserId: number): Promise<void> {
		const query = `
            INSERT INTO views (from_user_id, to_user_id)
            VALUES ($1, $2)
        `;
		await pool.query(query, [fromUserId, toUserId]);
	}

	public async getViewsByUserId(userId: number): Promise<View[]> {
		const query = `
            SELECT * FROM views
            WHERE to_user_id = $1
        `;
		const result = await pool.query(query, [userId]);
		return result.rows;
	}

	public async getViewCountByUserId(userId: number): Promise<number> {
		const query = `
        SELECT COUNT(*) AS view_count
        FROM views
        WHERE to_user_id = $1
    `;
		const result = await pool.query(query, [userId]);
		return parseInt(result.rows[0].view_count);
	}

	public async getViewCountByUserIdLastMonth(userId: number): Promise<number> {
		const query = `
        SELECT COUNT(*) AS view_count
        FROM views
        WHERE to_user_id = $1 AND created_at >= NOW() - INTERVAL '1 month'
    `;
		const result = await pool.query(query, [userId]);
		return parseInt(result.rows[0].view_count);
	}
}
