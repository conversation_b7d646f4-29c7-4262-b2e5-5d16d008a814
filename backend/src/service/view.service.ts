import pool from '../config/db';

export interface View {
	id: number;
	from_user_id: number;
	to_user_id: number;
	created_at: Date;
}

export const createView = async (
	fromUserId: number,
	toUserId: number
): Promise<void> => {
	const query = `
        INSERT INTO views (from_user_id, to_user_id)
        VALUES ($1, $2)
    `;
	await pool.query(query, [fromUserId, toUserId]);
};

export const getViewsByUserId = async (userId: number): Promise<View[]> => {
	const query = `
        SELECT * FROM views
        WHERE to_user_id = $1
    `;
	const result = await pool.query(query, [userId]);
	return result.rows;
};

export const getViewCountByUserId = async (userId: number): Promise<number> => {
	const query = `
        SELECT COUNT(*) AS view_count
        FROM views
        WHERE to_user_id = $1
    `;
	const result = await pool.query(query, [userId]);
	return parseInt(result.rows[0].view_count);
};

export const getViewCountByUserIdLastMonth = async (
	userId: number
): Promise<number> => {
	const query = `
        SELECT COUNT(*) AS view_count
        FROM views
        WHERE to_user_id = $1 AND created_at >= NOW() - INTERVAL '1 month'
    `;
	const result = await pool.query(query, [userId]);
	return parseInt(result.rows[0].view_count);
};
