import pool from '../config/db';
import Notification from '../model/notification.model';

export class NotificationService {
	public async createNotification(
		userId: number,
		title: string,
		content: string
	): Promise<Notification> {
		const client = await pool.connect();

		try {
			await client.query('BEGIN');

			// Step 1: Count existing notifications for the user
			const countResult = await client.query(
				`SELECT COUNT(*) FROM notifications WHERE user_id = $1`,
				[userId]
			);
			const count = parseInt(countResult.rows[0].count, 10);

			// Step 2: If 100 or more, delete the oldest one(s)
			if (count >= 100) {
				await client.query(
					`
				DELETE FROM notifications
				WHERE id IN (
					SELECT id FROM notifications
					WHERE user_id = $1
					ORDER BY created_at ASC
					LIMIT $2
				)
			`,
					[userId, count - 99] // keep 99 so that the new one becomes the 100th
				);
			}

			// Step 3: Insert the new notification
			const insertResult = await client.query(
				`
				INSERT INTO notifications (user_id, title, content)
				VALUES ($1, $2, $3)
				RETURNING *
			`,
				[userId, title, content]
			);

			await client.query('COMMIT');

			if (insertResult.rows.length === 0) {
				throw new Error('Failed to create notification');
			}

			return insertResult.rows[0];
		} catch (err) {
			await client.query('ROLLBACK');
			throw err;
		} finally {
			client.release();
		}
	}

	public async getNotificationsByUserId(
		userId: number
	): Promise<Notification[]> {
		const query = `
			SELECT * FROM notifications
			WHERE user_id = $1
			ORDER BY created_at DESC
		`;
		const result = await pool.query(query, [userId]);
		return result.rows;
	}

	public async getUnreadNotificationsByUserId(
		userId: number
	): Promise<Notification[]> {
		const query = `
            SELECT * FROM notifications
            WHERE user_id = $1 AND is_read = false
            ORDER BY created_at DESC
        `;
		const result = await pool.query(query, [userId]);
		return result.rows;
	}

	public async readAllNotifications(userId: number): Promise<void> {
		const query = `
            UPDATE notifications
            SET is_read = true
            WHERE user_id = $1 AND is_read = false
        `;
		await pool.query(query, [userId]);
	}
}
