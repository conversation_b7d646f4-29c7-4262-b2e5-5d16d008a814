import pool from '../config/db';
import { Message } from '../model/message.model';
import { User } from '../model/user.model';
import { sendNotification } from '../websocket/notification.websocket';

export class MessageService {
	public async createMessage(
		fromUser: User,
		toUserId: number,
		content: string
	): Promise<Message> {
		const query = `
			INSERT INTO messages (from_user_id, to_user_id, content)
			VALUES ($1, $2, $3)
			RETURNING *
		`;
		const result = await pool.query(query, [fromUser.id, toUserId, content]);
		if (result.rows.length === 0) {
			throw new Error('Failed to create message');
		}
		sendNotification(
			toUserId,
			'Message',
			`${fromUser.username} sent you a message!`,
			fromUser.id
		);
		return result.rows[0];
	}

	public async getMessagesBetweenUsers(
		userA: number,
		userB: number
	): Promise<Message[]> {
		const query = `
			SELECT * FROM messages
			WHERE 
				(from_user_id = $1 AND to_user_id = $2)
				OR
				(from_user_id = $2 AND to_user_id = $1)
			ORDER BY created_at ASC
		`;
		const result = await pool.query(query, [userA, userB]);
		return result.rows;
	}
}
