import pool from '../config/db';
import { Message } from '../model/message.model';

export class MessageService {
	public async createMessage(
		fromUserId: number,
		toUserId: number,
		content: string
	): Promise<void> {
		const query = `
            INSERT INTO messages (from_user_id, to_user_id, content)
            VALUES ($1, $2, $3)
        `;
		await pool.query(query, [fromUserId, toUserId, content]);
	}

	public async getMessagesBetweenUsers(
		userA: number,
		userB: number
	): Promise<Message[]> {
		const query = `
			SELECT * FROM messages
			WHERE 
				(from_user_id = $1 AND to_user_id = $2)
				OR
				(from_user_id = $2 AND to_user_id = $1)
			ORDER BY created_at ASC
		`;
		const result = await pool.query(query, [userA, userB]);
		return result.rows;
	}
}
