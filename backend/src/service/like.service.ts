import pool from '../config/db';
import { User } from '../model/user.model';
import { sendNotification } from '../websocket/notification.websocket';

export class LikeService {
	public async getLikes(userId: number): Promise<User[]> {
		const query = `
			SELECT u.*
			FROM users u
			INNER JOIN likes l ON u.id = l.from_user_id
			WHERE l.to_user_id = $1
		`;
		const result = await pool.query(query, [userId]);
		return result.rows;
	}

	public async getUsersILiked(userId: number): Promise<User[]> {
		const query = `
			SELECT u.*
			FROM users u
			INNER JOIN likes l ON u.id = l.to_user_id
			WHERE l.from_user_id = $1
		`;
		const result = await pool.query(query, [userId]);
		return result.rows;
	}

	public async createLike(fromUserId: number, toUserId: number): Promise<void> {
		const query = `
            INSERT INTO likes (from_user_id, to_user_id)
            VALUES ($1, $2)
        `;
		await pool.query(query, [fromUserId, toUserId]);
	}

	public async removeLike(fromUserId: number, toUserId: number): Promise<void> {
		const query = `
            DELETE FROM likes
            WHERE from_user_id = $1 AND to_user_id = $2
        `;
		await pool.query(query, [fromUserId, toUserId]);
	}

	public async isLiked(fromUserId: number, toUserId: number): Promise<boolean> {
		const query = `
			SELECT EXISTS (
				SELECT 1
				FROM likes
				WHERE from_user_id = $1 AND to_user_id = $2
			) AS is_liked
		`;
		const result = await pool.query(query, [fromUserId, toUserId]);
		return result.rows[0].is_liked;
	}

	public async toggleLike(formUser: User, toUser: User): Promise<void> {
		const isAlreadyLiked = await this.isLiked(formUser.id, toUser.id);

		if (isAlreadyLiked) {
			await this.removeLike(formUser.id, toUser.id);
			sendNotification(
				toUser.id,
				'Unlike',
				`${formUser.username} unlike you!`,
				formUser.id
			);
		} else {
			await this.createLike(formUser.id, toUser.id);
			sendNotification(
				toUser.id,
				'Like',
				`${formUser.username} liked you!`,
				formUser.id
			);

			const isMutual = await this.isMutualLike(formUser.id, toUser.id);
			if (isMutual) {
				sendNotification(
					toUser.id,
					'Match',
					`🎉 You matched with ${formUser.username}!`,
					formUser.id
				);
				sendNotification(
					formUser.id,
					'Match',
					`🎉 You matched with ${toUser.username}!`,
					toUser.id
				);
			}
		}
	}

	public async getMatchs(userId: number): Promise<User[]> {
		const query = `
			SELECT u.*
			FROM users u
			INNER JOIN likes l1 ON u.id = l1.to_user_id AND l1.from_user_id = $1
			INNER JOIN likes l2 ON l2.from_user_id = u.id AND l2.to_user_id = $1
		`;
		const result = await pool.query(query, [userId]);
		return result.rows;
	}

	public async isMutualLike(
		fromUserId: number,
		toUserId: number
	): Promise<boolean> {
		const query = `
            SELECT EXISTS (
                SELECT 1
                FROM likes l1
                JOIN likes l2
                ON l1.from_user_id = $1 AND l1.to_user_id = $2
                AND l2.from_user_id = $2 AND l2.to_user_id = $1
            ) AS match_exists
        `;
		const result = await pool.query(query, [fromUserId, toUserId]);
		return result.rows[0].match_exists;
	}
}
