import pool from '../config/db';
import { Like } from '../model/like.model';

export class LikeService {
	public async createLike(fromUserId: number, toUserId: number): Promise<void> {
		const query = `
            INSERT INTO likes (from_user_id, to_user_id)
            VALUES ($1, $2)
        `;
		await pool.query(query, [fromUserId, toUserId]);
	}

	public async removeLike(fromUserId: number, toUserId: number): Promise<void> {
		const query = `
            DELETE FROM likes
            WHERE from_user_id = $1 AND to_user_id = $2
        `;
		await pool.query(query, [fromUserId, toUserId]);
	}

	public async getLikesByUserId(userId: number): Promise<Like[]> {
		const query = `
            SELECT * FROM likes
            WHERE to_user_id = $1
        `;
		const result = await pool.query(query, [userId]);
		return result.rows;
	}

	public async getLikeCountByUserId(userId: number): Promise<number> {
		const query = `
            SELECT COUNT(*) AS like_count
            FROM likes
            WHERE to_user_id = $1
        `;
		const result = await pool.query(query, [userId]);
		return parseInt(result.rows[0].like_count);
	}

	public async getLikeCountByUserIdLastMonth(userId: number): Promise<number> {
		const query = `
            SELECT COUNT(*) AS like_count
            FROM likes
            WHERE to_user_id = $1 AND created_at >= NOW() - INTERVAL '1 month'
        `;
		const result = await pool.query(query, [userId]);
		return parseInt(result.rows[0].like_count);
	}

	public async isMutualLike(
		fromUserId: number,
		toUserId: number
	): Promise<boolean> {
		const query = `
            SELECT EXISTS (
                SELECT 1
                FROM likes l1
                JOIN likes l2
                ON l1.from_user_id = $1 AND l1.to_user_id = $2
                AND l2.from_user_id = $2 AND l2.to_user_id = $1
            ) AS match_exists
        `;
		const result = await pool.query(query, [fromUserId, toUserId]);
		return result.rows[0].match_exists;
	}
}
