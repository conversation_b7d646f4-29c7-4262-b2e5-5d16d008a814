import pool from '../config/db';

export interface Like {
	id: number;
	from_user_id: number;
	to_user_id: number;
	created_at: Date;
}

export const createLike = async (
	fromUserId: number,
	toUserId: number
): Promise<void> => {
	const query = `
        INSERT INTO likes (from_user_id, to_user_id)
        VALUES ($1, $2)
    `;
	await pool.query(query, [fromUserId, toUserId]);
};

export const removeLike = async (
	fromUserId: number,
	toUserId: number
): Promise<void> => {
	const query = `
        DELETE FROM likes
        WHERE from_user_id = $1 AND to_user_id = $2
    `;
	await pool.query(query, [fromUserId, toUserId]);
};

export const getLikesByUserId = async (userId: number): Promise<Like[]> => {
	const query = `
        SELECT * FROM likes
        WHERE to_user_id = $1
    `;
	const result = await pool.query(query, [userId]);
	return result.rows;
};

export const getLikeCountByUserId = async (userId: number): Promise<number> => {
	const query = `
        SELECT COUNT(*) AS like_count
        FROM likes
        WHERE to_user_id = $1
    `;
	const result = await pool.query(query, [userId]);
	return parseInt(result.rows[0].like_count);
};

export const getLikeCountByUserIdLastMonth = async (
	userId: number
): Promise<number> => {
	const query = `
        SELECT COUNT(*) AS like_count
        FROM likes
        WHERE to_user_id = $1 AND created_at >= NOW() - INTERVAL '1 month'
    `;
	const result = await pool.query(query, [userId]);
	return parseInt(result.rows[0].like_count);
};

export const isMutualLike = async (
	fromUserId: number,
	toUserId: number
): Promise<boolean> => {
	const query = `
        SELECT EXISTS (
            SELECT 1
            FROM likes l1
            JOIN likes l2
            ON l1.from_user_id = $1 AND l1.to_user_id = $2
            AND l2.from_user_id = $2 AND l2.to_user_id = $1
        ) AS match_exists
    `;
	const result = await pool.query(query, [fromUserId, toUserId]);
	return result.rows[0].match_exists;
};
