import pool from '../config/db';

export class ReportService {
	public async reportUser(reporterId: number, reportedUserId: number) {
		const query = `
            INSERT INTO reports (reporter_id, reported_user_id)
            VALUES ($1, $2)
        `;
		await pool.query(query, [reporterId, reportedUserId]);
	}

	public async unreportUser(reporterId: number, reportedUserId: number) {
		const query = `
            DELETE FROM reports
            WHERE reporter_id = $1 AND reported_user_id = $2
        `;
		await pool.query(query, [reporterId, reportedUserId]);
	}

	public async isReported(
		reporterId: number,
		reportedUserId: number
	): Promise<boolean> {
		const query = `
            SELECT EXISTS (
                SELECT 1
                FROM reports
                WHERE reporter_id = $1 AND reported_user_id = $2
            ) AS is_reported
        `;
		const result = await pool.query(query, [reporterId, reportedUserId]);
		return result.rows[0].is_reported;
	}

	public async toggleReport(
		reporterId: number,
		reportedUserId: number
	): Promise<void> {
		const isReported = await this.isReported(reporterId, reportedUserId);

		if (isReported) {
			await this.unreportUser(reporterId, reportedUserId);
		} else {
			await this.reportUser(reporterId, reportedUserId);
		}
	}
}
