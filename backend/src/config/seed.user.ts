import { faker } from '@faker-js/faker';
import bcrypt from 'bcryptjs';
import pool from './db';
import logger from '../utils/logger';
import axios from 'axios';
import crypto from 'crypto';

const GENDERS = ['Man', 'Woman', 'NonBinary', 'Others'] as const;

const INTERESTS_POOL = [
	'Coding',
	'Gaming',
	'Music',
	'Sports',
	'Cooking',
	'Travel',
	'Reading',
	'Movies',
	'Art',
	'Dancing',
	'Photography',
	'Fitness',
	'Technology',
	'Fashion',
	'Writing',
];

const LIVE_POOL = [
	{ country: 'Belgium', city: 'Brussels', x: 50.8503, y: 4.3517 },
	{ country: 'Belgium', city: 'Antwerp', x: 51.2194, y: 4.4025 },
	{ country: 'Belgium', city: 'Ghent', x: 51.0543, y: 3.7174 },
	{ country: 'Belgium', city: 'Bruges', x: 51.2093, y: 3.2247 },
	{ country: 'Belgium', city: 'Leuven', x: 50.8798, y: 4.7005 },
	{ country: 'Belgium', city: 'Namur', x: 50.4674, y: 4.8718 },
	{ country: 'Belgium', city: 'Liège', x: 50.6326, y: 5.5797 },
	{ country: 'Belgium', city: 'Mons', x: 50.4542, y: 3.9513 },
	{ country: 'Belgium', city: 'Charleroi', x: 50.4108, y: 4.4446 },
	{ country: 'Belgium', city: 'Tournai', x: 50.6056, y: 3.3878 },
	{ country: 'Belgium', city: 'Ostend', x: 51.23, y: 2.9126 },
	{ country: 'Belgium', city: 'Mechelen', x: 51.0256, y: 4.4777 },
	{ country: 'Belgium', city: 'Kortrijk', x: 50.8267, y: 3.2649 },
	{ country: 'Belgium', city: 'Sint-Niklaas', x: 51.1651, y: 4.1437 },
	{ country: 'Belgium', city: 'Hasselt', x: 50.9307, y: 5.3378 },
	{ country: 'Belgium', city: 'Leuven', x: 50.8798, y: 4.7005 },
	{ country: 'Belgium', city: 'Genk', x: 50.965, y: 5.5 },
];

function getRandomInterests() {
	const shuffled = [...INTERESTS_POOL].sort(() => 0.5 - Math.random());
	return shuffled.slice(0, faker.number.int({ min: 1, max: 5 }));
}

function getRandomSexualPreferences(gender: string) {
	return GENDERS.filter((g) => g !== gender)
		.sort(() => 0.5 - Math.random())
		.slice(0, faker.number.int({ min: 1, max: GENDERS.length - 1 }));
}

async function downloadProfilePicture(): Promise<{
	data: Buffer;
	contentType: string;
} | null> {
	try {
		// Random male or female portrait from randomuser.me
		const gender = Math.random() < 0.5 ? 'men' : 'women';
		const index = Math.floor(Math.random() * 99);
		const url = `https://randomuser.me/api/portraits/${gender}/${index}.jpg`;

		const response = await axios.get(url, { responseType: 'arraybuffer' });
		return {
			data: Buffer.from(response.data),
			contentType: response.headers['content-type'] || 'image/jpeg',
		};
	} catch (error) {
		logger.warn('[SEED] Failed to download profile picture:', error);
		return null;
	}
}

async function seedUsers(count: number) {
	const password = crypto.randomBytes(16).toString('hex');
	const hashedPassword = bcrypt.hashSync(password, 10);

	for (let i = 0; i < count; i++) {
		const firstName = faker.person.firstName();
		const lastName = faker.person.lastName();
		const username = faker.internet.username();
		const email = faker.internet.email();
		const birthdate = faker.date.birthdate({ min: 18, max: 60, mode: 'age' });
		const gender = faker.helpers.arrayElement(GENDERS);
		const sexualPreference = getRandomSexualPreferences(gender);
		const interests = getRandomInterests();
		const location = faker.helpers.arrayElement(LIVE_POOL);

		try {
			// Insert user with location
			const result = await pool.query(
				`INSERT INTO users (
					first_name, last_name, username, email, birthdate,
					password, gender, sexual_preference, interests,
					email_verified, country, city, latitude, longitude
				) VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10,$11,$12,$13,$14)
				RETURNING id`,
				[
					firstName,
					lastName,
					username,
					email,
					birthdate,
					hashedPassword,
					gender,
					sexualPreference,
					interests,
					true,
					location.country,
					location.city,
					location.x,
					location.y,
				]
			);

			const userId = result.rows[0].id;

			// Download and insert profile picture
			const pic = await downloadProfilePicture();
			if (pic) {
				await pool.query(
					`INSERT INTO user_pictures (user_id, profile_picture, data, content_type)
					VALUES ($1, true, $2, $3)`,
					[userId, pic.data, pic.contentType]
				);
			}

			logger.info(`[SEED] Inserted user ${i + 1}: ${username}`);
		} catch (error) {
			logger.error(`Error inserting user ${i + 1}:`, error);
		}
	}
	logger.info('[SEED] Seeding complete!');
}

export async function populateXtrem(int: number = 500) {
	await seedUsers(int);
}

export default populateXtrem;
