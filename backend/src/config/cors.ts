const corsOptions = {
	origin: function (
		origin: string | undefined,
		callback: (err: Error | null, allow?: boolean) => void
	) {
		const allowedOrigins = [
			'http://127.0.0.1',
			'http://localhost',
			'https://matcha.mvpee.be',
		];

		if (!origin) return callback(null, true);

		const isAllowed = allowedOrigins.some((allowedOrigin) =>
			origin.startsWith(allowedOrigin)
		);

		isAllowed
			? callback(null, true)
			: callback(new Error('Not allowed by CORS'));
	},
	optionsSuccessStatus: 200,
};

export default corsOptions;
