import { Pool } from 'pg';
import bcrypt from 'bcryptjs';
import logger from '../utils/logger';

const pool = new Pool({
	host: process.env.POSTGRES_HOST,
	database: process.env.POSTGRES_DB,
	user: process.env.POSTGRES_USER,
	password: process.env.POSTGRES_PASSWORD,
	port: 5432,
});

const dropTables = async () => {
	try {
		await pool.query(`
				DO
				$$
				DECLARE
					r RECORD;
				BEGIN
					FOR r IN (SELECT tablename FROM pg_tables WHERE schemaname = 'public') LOOP
						EXECUTE 'DROP TABLE IF EXISTS ' || quote_ident(r.tablename) || ' CASCADE';
					END LOOP;
					FOR r IN (SELECT typname FROM pg_type WHERE typcategory = 'E' AND typnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public')) LOOP
						EXECUTE 'DROP TYPE IF EXISTS ' || quote_ident(r.typname) || ' CASCADE';
					END LOOP;
				END;
				$$;
			`);

		logger.info('[DEV] Database tables dropped successfully');
	} catch (error) {
		logger.error('Error dropping tables:', error);
	}
};

const createTables = async () => {
	try {
		await pool.query(`

			CREATE TYPE gender AS ENUM ('Man', 'Woman', 'NonBinary', 'Others');

			CREATE TABLE IF NOT EXISTS users (
				id SERIAL PRIMARY KEY,
				first_name VARCHAR(50) NOT NULL,
				last_name VARCHAR(50) NOT NULL,
				username VARCHAR(50) NOT NULL UNIQUE,
				email VARCHAR(100) NOT NULL UNIQUE,
				password TEXT NOT NULL,
				email_verification_token VARCHAR(128),
				email_verified BOOLEAN DEFAULT FALSE,
				password_reset_token VARCHAR(128),
				location POINT,
				gender gender,
				sexual_preference gender[],
				biography VARCHAR(512),
				interests VARCHAR(20)[],
				created_at TIMESTAMP DEFAULT NOW()
			);

			CREATE TABLE IF NOT EXISTS user_pictures (
				id SERIAL PRIMARY KEY,
				user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
				picture BYTEA NOT NULL,
				created_at TIMESTAMP DEFAULT NOW()
			);

			CREATE TABLE IF NOT EXISTS views (
				id BIGSERIAL PRIMARY KEY,
				from_user_id BIGINT REFERENCES users(id) ON DELETE CASCADE,
				to_user_id BIGINT REFERENCES users(id) ON DELETE CASCADE,
				created_at TIMESTAMP DEFAULT NOW()
			);

			CREATE TABLE IF NOT EXISTS likes (
				id BIGSERIAL PRIMARY KEY,
				from_user_id BIGINT REFERENCES users(id) ON DELETE CASCADE,
				to_user_id BIGINT REFERENCES users(id) ON DELETE CASCADE,
				created_at TIMESTAMP DEFAULT NOW()
			);

			CREATE TABLE IF NOT EXISTS messages (
				id BIGSERIAL PRIMARY KEY,
				sender_id BIGINT REFERENCES users(id) ON DELETE CASCADE,
				receiver_id BIGINT REFERENCES users(id) ON DELETE CASCADE,
				message VARCHAR(100) NOT NULL,
				created_at TIMESTAMP DEFAULT NOW()
			);
		`);

		logger.info('[DEV] Database tables created successfully');
	} catch (error) {
		logger.error('Error creating database tables:', error);
	}
};

const populateTables = async () => {
	try {
		let hashedPassword = bcrypt.hashSync(
			process.env.USER_MVAN_PEE_PASSWORD || 'password',
			10
		);
		await pool.query(
			`INSERT INTO users (first_name, last_name, username, email, password, gender, sexual_preference, location, interests)
			VALUES ($1, $2, $3, $4, $5, $6, $7, POINT($8, $9), $10);`,
			[
				'Marius',
				'Van Pee',
				'mvan-pee',
				'<EMAIL>',
				hashedPassword,
				'Man',
				['Woman', 'NonBinary'],
				4.4699,
				50.5039,
				['Coding', 'Gaming', 'Music'],
			]
		);

		hashedPassword = bcrypt.hashSync(
			process.env.USER_RPEREZ_T_PASSWORD || 'password',
			10
		);

		await pool.query(
			`INSERT INTO users (first_name, last_name, username, email, password, gender, sexual_preference, location, interests)
			VALUES ($1, $2, $3, $4, $5, $6, $7, POINT($8, $9), $10);`,
			[
				'Rodolfo',
				'Alberto Perez Tobar',
				'rperez-t',
				'<EMAIL>',
				hashedPassword,
				'Man',
				['Woman'],
				4.4699,
				50.5039,
				['Coding', 'Gaming', 'Music'],
			]
		);

		logger.info('[DEV] Database tables populated successfully');
	} catch (error) {
		logger.error('Error populating database tables:', error);
	}
};

const migrations = async () => {
	try {
	} catch (error) {
		logger.error('Error during database migrations:', error);
	}
};

const waitForPostgres = async (retries = 10, delay = 2000): Promise<void> => {
	for (let i = 0; i < retries; i++) {
		try {
			await pool.query('SELECT 1');
			logger.info('[INIT] PostgreSQL is ready');
			return;
		} catch (err: any) {
			logger.warn(`[INIT] Waiting for PostgreSQL... (${i + 1}/${retries})`);
			await new Promise((res) => setTimeout(res, delay));
		}
	}
	throw new Error('PostgreSQL is not ready after multiple attempts');
};

const init = async () => {
	await waitForPostgres();
	logger.info('[INIT] PostgreSQL connection established');
	if (process.env.ENV === 'dev') {
		await dropTables();
		await createTables();
		await populateTables();
	} else if (process.env.ENV === 'prod') {
		await migrations();
	}
};

init();

export default pool;
