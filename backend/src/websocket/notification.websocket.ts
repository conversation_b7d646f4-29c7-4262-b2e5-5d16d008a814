import { IncomingMessage } from 'http';
import { WebSocket, WebSocketServer } from 'ws';
import { AuthMiddleware } from '../middleware/auth/auth.middleware';
import logger from '../utils/logger';
import { NotificationService } from '../service/notification.service';
import Notification from '../model/notification.model';

declare module 'ws' {
	interface WebSocket {
		userId?: number;
		username?: string;
	}
}

const authMiddleware = new AuthMiddleware();
const notificationService = new NotificationService();

export const notificationWss = new WebSocketServer({
	noServer: true,
});

const room = new Set<WebSocket>();

notificationWss.on(
	'connection',
	async (ws: WebSocket, request: IncomingMessage) => {
		const url = new URL(request.url || '', `ws://${request.headers.host}`);
		const token = url.searchParams.get('token');

		if (!token) {
			ws.close(1008, 'Missing token');
			return;
		}

		const user = await authMiddleware.getAuthenticatedUserByToken(token);
		if (!user) {
			ws.close(1008, 'Invalid token');
			return;
		}

		const userId = user.id;

		ws.userId = userId;
		ws.username = user.username;

		room.add(ws);
		logger.debug(`[CONNECTION] The user ${user.username} is now connected`);

		ws.on('message', async (message: string) => {
			try {
				const parsed = JSON.parse(message);
				if (parsed.type === 'ping') ws.pong();
				else logger.info(`${user.username}: ${parsed.content}`);
			} catch (err) {
				logger.warn('Invalid WebSocket message format');
			}
		});

		ws.on('close', () => {
			logger.debug(
				`[CONNECTION] The user ${user.username} is now disconnected`
			);
			room.delete(ws);
		});

		ws.on('error', (error) => {
			logger.error(`WebSocket error for ${user.username}:`, error);
			ws.close();
		});
	}
);

const viewMap = new Map<number, Date>();
const unlikeMap = new Map<number, Date>();
const likeMap = new Map<number, Date>();
const matchMap = new Map<string, Date>();
const COOLDOWN = 1000 * 60 * 60; // 1 hour cooldown

export async function sendNotification(
	userId: number,
	title: string,
	content: string,
	fromUserId: number | null = null
) {
	const now = new Date();

	switch (title) {
		case 'View': {
			const lastView = viewMap.get(userId);
			if (lastView && now.getTime() - lastView.getTime() < COOLDOWN) {
				logger.debug(
					`Skipping notification for ${userId} due to view cooldown`
				);
				return;
			}
			viewMap.set(userId, now);
			break;
		}
		case 'Unlike': {
			const lastUnlike = unlikeMap.get(userId);
			if (lastUnlike && now.getTime() - lastUnlike.getTime() < COOLDOWN) {
				logger.debug(
					`Skipping notification for ${userId} due to unlike cooldown`
				);
				return;
			}
			unlikeMap.set(userId, now);
			break;
		}
		case 'Like': {
			const lastLike = likeMap.get(userId);
			if (lastLike && now.getTime() - lastLike.getTime() < COOLDOWN) {
				logger.debug(
					`Skipping notification for ${userId} due to like cooldown`
				);
				return;
			}
			likeMap.set(userId, now);
			break;
		}
		case 'Message':
			const { rooms, getRoomName } = await import('./chat.websocket');
			const roomName = getRoomName(fromUserId!, userId);
			const chatRoom = rooms.get(roomName);
			if (chatRoom && chatRoom.size >= 2) {
				// Don't need to send notification if they are already in the chatroom
				return;
			}
			break;
		case 'Match':
			const lastMatch = matchMap.get(content);
			if (lastMatch && now.getTime() - lastMatch.getTime() < COOLDOWN) {
				logger.debug(
					`Skipping notification for ${userId} due to match cooldown`
				);
				return;
			}
			matchMap.set(content, now);
			break;
		default:
			logger.warn(`Unknown notification title: ${title}`);
			return;
	}

	const notification: Notification | null =
		await notificationService.createNotification(userId, title, content);
	for (const client of room) {
		if (client.userId == userId) {
			if (client.readyState === WebSocket.OPEN) {
				client.send(JSON.stringify(notification));
				logger.debug(
					`Notification sent to user ${client.username}: ${content}`
				);
			}
		}
	}
}
