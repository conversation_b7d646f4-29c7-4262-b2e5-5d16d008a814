import { WebSocketServer, WebSocket } from 'ws';
import logger from '../utils/logger';
import { IncomingMessage } from 'http';
import { AuthMiddleware } from '../middleware/auth/auth.middleware';
import { UserService } from '../service/user.service';
import { LikeService } from '../service/like.service';
import { MessageService } from '../service/message.service';
import { Message } from '../model/message.model';

const authMiddleware = new AuthMiddleware();
const userService = new UserService();
const likeService = new LikeService();
const messageService = new MessageService();

export const chatWss = new WebSocketServer({
	noServer: true,
});

export const rooms = new Map<string, Set<WebSocket>>();

// Rate limiting: Map of userId to array of message timestamps
const messageRateLimit = new Map<number, number[]>();
const RATE_LIMIT_WINDOW = 60000; // 1 minute
const MAX_MESSAGES_PER_WINDOW = 30; // 30 messages per minute

function isRateLimited(userId: number): boolean {
	const now = Date.now();
	const userMessages = messageRateLimit.get(userId) || [];

	// Remove old messages outside the window
	const recentMessages = userMessages.filter(
		(timestamp) => now - timestamp < RATE_LIMIT_WINDOW
	);

	if (recentMessages.length >= MAX_MESSAGES_PER_WINDOW) {
		return true;
	}

	// Add current message timestamp
	recentMessages.push(now);
	messageRateLimit.set(userId, recentMessages);

	return false;
}

function sanitizeMessageContent(content: string): string {
	if (!content || typeof content !== 'string') {
		return '';
	}

	// Trim and limit length
	content = content.trim().substring(0, 100);

	// Remove potentially dangerous content
	content = content
		.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
		.replace(/javascript:/gi, '')
		.replace(/on\w+\s*=/gi, '')
		.replace(/data:/gi, '');

	return content;
}

function validateMessageContent(content: string): {
	isValid: boolean;
	error?: string;
} {
	if (!content || content.trim().length === 0) {
		return { isValid: false, error: 'Message cannot be empty' };
	}

	if (content.length > 100) {
		return { isValid: false, error: 'Message too long' };
	}

	// Check for suspicious patterns
	const suspiciousPatterns = [
		/<script/i,
		/javascript:/i,
		/on\w+\s*=/i,
		/data:text\/html/i,
		/vbscript:/i,
	];

	for (const pattern of suspiciousPatterns) {
		if (pattern.test(content)) {
			return { isValid: false, error: 'Invalid content' };
		}
	}

	return { isValid: true };
}

export function getRoomName(fromUserId: number, toUserId: number): string {
	return `chat_${Math.min(fromUserId, toUserId)}_${Math.max(
		fromUserId,
		toUserId
	)}`;
}

chatWss.on('connection', async (ws: WebSocket, request: IncomingMessage) => {
	const url = new URL(request.url || '', `ws://${request.headers.host}`);
	const token = url.searchParams.get('token');
	const recipientUserId = parseInt(url.searchParams.get('userId') || '');

	if (!token || isNaN(recipientUserId)) {
		ws.close(1008, 'Missing token or invalid userId');
		return;
	}

	const fromUser = await authMiddleware.getAuthenticatedUserByToken(token);
	if (!fromUser) {
		ws.close(1008, 'Invalid token');
		return;
	}

	const recipientUser = await userService.getUserById(recipientUserId);
	if (!recipientUser) {
		ws.close(1008, 'Recipient user not found');
		return;
	}

	if (!(await likeService.isMutualLike(fromUser.id, recipientUser.id))) {
		ws.close(1008, 'No mutual like');
		return;
	}

	const roomName = getRoomName(fromUser.id, recipientUser.id);
	if (!rooms.has(roomName)) {
		rooms.set(roomName, new Set<WebSocket>());
	}
	const room = rooms.get(roomName)!;
	room.add(ws);

	logger.info(
		`Chat connection established for ${fromUser.username} with ${recipientUser.username} in room ${roomName}`
	);

	ws.on('message', async (message: string) => {
		try {
			// Rate limiting check
			if (isRateLimited(fromUser.id)) {
				logger.warn(`Rate limit exceeded for user ${fromUser.username}`);
				ws.send(
					JSON.stringify({ error: 'Rate limit exceeded. Please slow down.' })
				);
				return;
			}

			const parsed = JSON.parse(message.toString());

			// Pong response for ping
			if (parsed.type === 'ping') {
				ws.pong();
				return;
			}

			// Validate message content
			const validation = validateMessageContent(parsed.content);
			if (!validation.isValid) {
				logger.warn(
					`[WAF] Invalid message from ${fromUser.username}: ${validation.error}`
				);
				ws.send(JSON.stringify({ error: validation.error }));
				return;
			}

			// Sanitize content
			const sanitizedContent = sanitizeMessageContent(parsed.content);
			if (!sanitizedContent) {
				ws.send(JSON.stringify({ error: 'Message content is invalid' }));
				return;
			}

			// Check mutual like
			if (!(await likeService.isMutualLike(fromUser.id, recipientUser.id))) {
				logger.debug(
					`Message from ${fromUser.username} to ${recipientUser.username} rejected due to no mutual like`
				);
				ws.close(1008, 'No mutual like');
				return;
			}

			logger.info(
				`${fromUser.username} to ${recipientUser.username}: ${sanitizedContent}`
			);

			const msg: Message = await messageService.createMessage(
				fromUser,
				recipientUser.id,
				sanitizedContent
			);

			room.forEach((client) => {
				if (client !== ws && client.readyState === WebSocket.OPEN) {
					client.send(JSON.stringify(msg));
				}
			});
		} catch (error) {
			logger.error(
				`Error processing message from ${fromUser.username}:`,
				error
			);
			ws.send(JSON.stringify({ error: 'Failed to process message' }));
		}
	});

	ws.on('close', () => {
		logger.info(
			`${fromUser.username} left chat room with ${recipientUser.username}`
		);
		room.delete(ws);
		if (room.size === 0) {
			rooms.delete(roomName);
			logger.info(`Room ${roomName} closed (no participants)`);
		}
	});

	ws.on('error', (error) => {
		logger.error(`WebSocket error for ${fromUser.username}:`, error);
		ws.close();
	});
});
