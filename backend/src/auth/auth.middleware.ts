import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';

const JWT_SECRET = process.env.JWT_SECRET || 'secret';

interface JwtPayloadExtended {
	id: string;
	username: string;
	email: string;
}

export function authenticateToken(
	req: Request,
	res: Response,
	next: NextFunction
) {
	const authHeader = req.headers['authorization'];
	const token = authHeader?.split(' ')[1];

	if (!token) return res.status(401).json({ error: 'Missing token' });

	try {
		const decoded = jwt.verify(token, JWT_SECRET) as JwtPayloadExtended;

		(req as Request & { user?: JwtPayloadExtended }).user = decoded;

		next();
	} catch {
		res.status(403).json({ error: 'Invalid token' });
	}
}
