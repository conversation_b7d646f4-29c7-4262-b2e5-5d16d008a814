import express from 'express';
const router = express.Router();
import logger from '../../utils/logger';
import { authenticateToken } from '../../auth/auth.middleware';
import { getUserById } from '../../service/user.service';
import { sanitizeUser } from '../../sanitize/user.sanitize';

router.get('/:id', authenticateToken, async (req, res) => {
	const id = parseInt(req.params.id, 10);

	logger.info(`GET /user/${id}`);

	if (isNaN(id)) return res.status(400).json({ error: 'Invalid user ID' });

	try {
		const user = await getUserById(id);
		if (!user) return res.status(404).json({ error: 'User not found' });

		return res.status(200).json(sanitizeUser(user));
	} catch (error: any) {
		logger.error(`Error fetching user by ID: ${error.message}`);
		return res.status(500).json({ error: 'Internal server error' });
	}
});

export default router;
