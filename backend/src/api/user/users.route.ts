import express, { Request, Response } from 'express';
const router = express.Router();
import logger from '../../utils/logger';
import { AuthMiddleware } from '../../middleware/auth/auth.middleware';
import { UserService } from '../../service/user.service';
import { sanitizeUser, sanitizeUsers } from '../../sanitize/user.sanitize';
import { GENDERS } from '../../model/enum/gender.enum';
import upload from '../../middleware/upload/upload';
import { UserPicture } from '../../model/user-picture.model';

const userService = new UserService();
const authMiddleware = new AuthMiddleware();

/**
 * @swagger
 * /api/users/suggestions:
 *   get:
 *     summary: Get suggested matches for the authenticated user
 *     tags:
 *       - Users
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: List of suggested matches
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.get(
	'/suggestions',
	authMiddleware.authenticateToken,
	async (req: Request, res: Response) => {
		// Get options from query parameters
		const options = {
			maxDistanceKm: parseFloat(req.query.maxDistanceKm as string) || 30,
			limit: parseInt(req.query.limit as string) || 20,
			offset: parseInt(req.query.offset as string) || 0,
			ageGap: parseInt(req.query.ageGap as string) || 50,
		};

		logger.info(
			`GET /users/suggestions with options: ${JSON.stringify(options)}`
		);

		if (
			isNaN(options.maxDistanceKm) ||
			isNaN(options.limit) ||
			isNaN(options.offset)
		) {
			return res.status(400).json({ error: 'Invalid query parameters' });
		}
		try {
			const currentUser = await authMiddleware.getAuthenticatedUser(req);
			if (!currentUser) return res.status(401).json({ error: 'Unauthorized' });

			logger.info(`GET /users/suggestions for ${currentUser.username}`);

			const suggestions = await userService.getSuggestedMatches(
				currentUser,
				options
			);

			return res.status(200).json(sanitizeUsers(suggestions));
		} catch (error: any) {
			logger.error(`Error fetching suggestions: ${error.message}`);
			return res.status(500).json({ error: 'Internal server error' });
		}
	}
);

/**
 * @swagger
 * /api/users/{id}:
 *   get:
 *     summary: Gets a user by ID
 *     tags:
 *       - Users
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Success
 *       400:
 *         description: Bad request
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: User not found
 *       500:
 *         description: Internal server error
 */
router.get('/:id', authMiddleware.authenticateToken, async (req, res) => {
	const id = parseInt(req.params.id, 10);

	logger.info(`GET /user/${id}`);

	if (isNaN(id)) return res.status(400).json({ error: 'Invalid user ID' });

	try {
		const user = await userService.getUserById(id);
		if (!user) return res.status(404).json({ error: 'User not found' });

		return res.status(200).json(sanitizeUser(user));
	} catch (error: any) {
		logger.error(`Error fetching user by ID: ${error.message}`);
		return res.status(500).json({ error: 'Internal server error' });
	}
});

router.post(
	'/complete-profile',
	authMiddleware.authenticateToken,
	upload.array('pictures', 5),
	async (req: Request, res: Response) => {
		try {
			const user = await authMiddleware.getAuthenticatedUser(req);

			logger.info(`POST /complete-profile ${user!.username}`);

			const { gender, sexual_preference, biography, interests, birthdate } =
				req.body as any;

			const parsedPreferences = JSON.parse(sexual_preference);
			const parsedInterests = JSON.parse(interests);

			const isValidGender = GENDERS.includes(gender);
			const isValidPreferences =
				Array.isArray(parsedPreferences) &&
				parsedPreferences.every((g) => GENDERS.includes(g)) &&
				parsedPreferences.length > 0;

			if (!isValidGender || !isValidPreferences) {
				return res.status(400).json({ error: 'Invalid gender or preferences' });
			}

			if (
				typeof biography !== 'string' ||
				biography.length > 512 ||
				biography.length === 0
			) {
				return res.status(400).json({
					error:
						'Biography needs to be a string with a maximum of 512 characters and cannot be empty',
				});
			}

			if (
				!Array.isArray(parsedInterests) ||
				!parsedInterests.every(
					(i) => typeof i === 'string' && i.length <= 20 && i.length > 1
				)
			) {
				return res.status(400).json({
					error: 'Invalid interests format.',
				});
			}

			if (parsedInterests.length === 0) {
				return res.status(400).json({ error: 'Interests cannot be empty' });
			}

			if (parsedInterests.length > 5) {
				return res.status(400).json({ error: 'Maximum 5 interests allowed' });
			}

			const birth = new Date(birthdate);
			const today = new Date();
			const age = today.getFullYear() - birth.getFullYear();
			const m = today.getMonth() - birth.getMonth();
			const isBirthdayPassed =
				m > 0 || (m === 0 && today.getDate() >= birth.getDate());
			const actualAge = isBirthdayPassed ? age : age - 1;
			if (!birthdate || isNaN(Date.parse(birthdate))) {
				return res.status(400).json({ error: 'Invalid birthdate' });
			}
			if (actualAge < 18) {
				return res
					.status(400)
					.json({ error: 'You must be at least 18 years old' });
			}

			const pictures = req.files as Express.Multer.File[];
			if (pictures.length > 5) {
				return res.status(400).json({ error: 'Maximum 5 pictures allowed' });
			}

			if (pictures.length === 0) {
				return res
					.status(400)
					.json({ error: 'At least one picture for profile is required' });
			}

			for (const picture of pictures) {
				if (
					!['image/jpeg', 'image/png', 'image/webp'].includes(picture.mimetype)
				) {
					return res.status(400).json({
						error: 'Only JPEG, PNG, and WEBP image formats are allowed.',
					});
				}
			}

			for (const picture of pictures) {
				if (picture.size > 5 * 1024 * 1024) {
					return res.status(400).json({
						error: 'Each picture must be less than 5 MB.',
					});
				}
			}

			for (let i = 0; i < pictures.length; i++) {
				await userService.addUserPicture(
					user!.id,
					pictures[i].buffer,
					pictures[i].mimetype,
					i === 0
				);
			}

			await userService.completeUserProfile(user!.id, {
				gender,
				sexual_preference: parsedPreferences,
				biography,
				interests: parsedInterests,
				birthdate: new Date(birthdate),
			});

			res.status(200).json({ message: 'Profile completed successfully' });
		} catch (error: any) {
			logger.error(`Error completing profile: ${error.message}`);
			res.status(500).json({ error: 'Internal server error' });
		}
	}
);

/* Picture */
router.get('/:id/profile-picture', async (req, res) => {
	const id = parseInt(req.params.id, 10);

	logger.info(`GET /user/${id}/profile-picture`);

	if (isNaN(id)) return res.status(400).json({ error: 'Invalid user ID' });

	try {
		const user = await userService.getUserById(id);
		if (!user) return res.status(404).json({ error: 'User not found' });

		const pictureCount = await userService.countUserPictures(user.id);
		if (pictureCount === 0) {
			return res.status(404).json({ error: 'No profile picture found' });
		}

		const pictures: UserPicture | null =
			await userService.getUserProfilePicture(user.id);
		if (!pictures) {
			return res.status(404).json({ error: 'Profile picture not found' });
		}
		res.set('Content-Type', pictures.content_type);
		return res.send(pictures.data);
	} catch (error: any) {
		logger.error(`Error fetching user profile picture: ${error.message}`);
		return res.status(500).json({ error: 'Internal server error' });
	}
});

router.get(
	'/:id/picture-ids',
	authMiddleware.authenticateToken,
	async (req, res) => {
		const id = parseInt(req.params.id, 10);

		if (isNaN(id)) {
			return res.status(400).json({ error: 'Invalid user ID' });
		}

		try {
			const user = await userService.getUserById(id);
			if (!user) {
				return res.status(404).json({ error: 'User not found' });
			}

			const pictureIds = await userService.getUsersPicturesId(user.id);
			const idsOnly = pictureIds.map((pic) => pic.id);

			return res.status(200).json({ pictureIds: idsOnly });
		} catch (error: any) {
			logger.error(`Error fetching picture IDs: ${error.message}`);
			return res.status(500).json({ error: 'Internal server error' });
		}
	}
);

router.get('/pictures/:pictureId', async (req, res) => {
	const pictureId = parseInt(req.params.pictureId, 10);

	if (isNaN(pictureId)) {
		return res.status(400).json({ error: 'Invalid picture ID' });
	}

	try {
		const picture = await userService.getPictureById(pictureId);
		if (!picture) {
			return res.status(404).json({ error: 'Picture not found' });
		}

		res.set('Content-Type', picture.content_type);
		return res.send(picture.data);
	} catch (error: any) {
		logger.error(`Error fetching picture by ID: ${error.message}`);
		return res.status(500).json({ error: 'Internal server error' });
	}
});

export default router;
