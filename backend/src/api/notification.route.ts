import express, { Request, Response } from 'express';
import { AuthMiddleware } from '../middleware/auth/auth.middleware';
import logger from '../utils/logger';
import { NotificationService } from '../service/notification.service';
const router = express.Router();

const authMiddleware = new AuthMiddleware();
const notificationService = new NotificationService();

router.get(
	'/',
	authMiddleware.authenticateToken,
	async (req: Request, res: Response) => {
		try {
			const user = await authMiddleware.getAuthenticatedUser(req);
			if (!user) {
				return res.status(401).json({ error: 'Unauthorized' });
			}

			logger.info(`GET /notification`);

			const notifications = await notificationService.getNotificationsByUserId(
				user.id
			);
			return res.status(200).json(notifications);
		} catch (error) {
			logger.error('Error fetching notifications:', error);
			return res.status(500).json({ error: 'Internal server error' });
		}
	}
);

router.get(
	'/unread',
	authMiddleware.authenticateToken,
	async (req: Request, res: Response) => {
		try {
			const user = await authMiddleware.getAuthenticatedUser(req);
			if (!user) {
				return res.status(401).json({ error: 'Unauthorized' });
			}

			logger.info(`GET /notification/unread`);

			const notifications =
				await notificationService.getUnreadNotificationsByUserId(user.id);
			return res.status(200).json(notifications);
		} catch (error) {
			logger.error('Error fetching unread notifications:', error);
			return res.status(500).json({ error: 'Internal server error' });
		}
	}
);

router.put(
	'/read',
	authMiddleware.authenticateToken,
	async (req: Request, res: Response) => {
		try {
			const user = await authMiddleware.getAuthenticatedUser(req);
			if (!user) {
				return res.status(401).json({ error: 'Unauthorized' });
			}

			logger.info(`PUT /notification/read`);

			await notificationService.readAllNotifications(user.id);
		} catch (error) {
			logger.error('Error creating notification:', error);
			return res.status(500).json({ error: 'Internal server error' });
		}
	}
);

export default router;
