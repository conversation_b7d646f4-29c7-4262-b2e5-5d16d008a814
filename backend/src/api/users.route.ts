import express, { Request, Response } from 'express';
const router = express.Router();
import logger from '../utils/logger';
import bcrypt from 'bcryptjs';
import { AuthMiddleware } from '../middleware/auth/auth.middleware';
import { UserService } from '../service/user.service';
import { sanitizeUser, sanitizeUsers } from '../sanitize/user.sanitize';
import { GENDERS } from '../model/enum/gender.enum';
import upload from '../middleware/upload/upload';
import { UserPicture } from '../model/user-picture.model';
import { isStrongPassword, isValidEmail } from '../utils/validation';

const userService = new UserService();
const authMiddleware = new AuthMiddleware();

/**
 * @swagger
 * /api/users/suggestions:
 *   get:
 *     summary: Get suggested matches for the authenticated user
 *     tags:
 *       - Users
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: List of suggested matches
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.get(
	'/suggestions',
	authMiddleware.authenticateToken,
	async (req: Request, res: Response) => {
		// Get options from query parameters
		const options = {
			maxDistanceKm: parseFloat(req.query.maxDistanceKm as string) || 30,
			limit: parseInt(req.query.limit as string) || 20,
			offset: parseInt(req.query.offset as string) || 0,
			ageGap: parseInt(req.query.ageGap as string) || 50,
		};

		logger.info(
			`GET /users/suggestions with options: ${JSON.stringify(options)}`
		);

		if (
			isNaN(options.maxDistanceKm) ||
			isNaN(options.limit) ||
			isNaN(options.offset)
		) {
			return res.status(400).json({ error: 'Invalid query parameters' });
		}
		try {
			const currentUser = await authMiddleware.getAuthenticatedUser(req);
			if (!currentUser) return res.status(401).json({ error: 'Unauthorized' });

			logger.info(`GET /users/suggestions for ${currentUser.username}`);

			const suggestions = await userService.getSuggestedMatches(
				currentUser,
				options
			);

			return res.status(200).json(sanitizeUsers(suggestions));
		} catch (error: any) {
			logger.error(`Error fetching suggestions: ${error.message}`);
			return res.status(500).json({ error: 'Internal server error' });
		}
	}
);

/**
 * @swagger
 * /api/users/{id}:
 *   get:
 *     summary: Gets a user by ID
 *     tags:
 *       - Users
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Success
 *       400:
 *         description: Bad request
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: User not found
 *       500:
 *         description: Internal server error
 */
router.get('/:id', authMiddleware.authenticateToken, async (req, res) => {
	const id = parseInt(req.params.id, 10);

	logger.info(`GET /users/${id}`);

	if (isNaN(id)) return res.status(400).json({ error: 'Invalid user ID' });

	try {
		const user = await userService.getUserById(id);
		if (!user) return res.status(404).json({ error: 'User not found' });

		return res.status(200).json(sanitizeUser(user));
	} catch (error: any) {
		logger.error(`Error fetching user by ID: ${error.message}`);
		return res.status(500).json({ error: 'Internal server error' });
	}
});

router.post(
	'/complete-profile',
	authMiddleware.authenticateToken,
	upload.array('pictures', 5),
	async (req: Request, res: Response) => {
		try {
			const user = await authMiddleware.getAuthenticatedUser(req);

			logger.info(`POST /users/complete-profile ${user!.username}`);

			const { gender, sexual_preference, biography, interests, birthdate } =
				req.body as any;

			const parsedPreferences = JSON.parse(sexual_preference);
			const parsedInterests = JSON.parse(interests);

			const isValidGender = GENDERS.includes(gender);
			const isValidPreferences =
				Array.isArray(parsedPreferences) &&
				parsedPreferences.every((g) => GENDERS.includes(g)) &&
				parsedPreferences.length > 0;

			if (!isValidGender || !isValidPreferences) {
				return res.status(400).json({ error: 'Invalid gender or preferences' });
			}

			if (
				typeof biography !== 'string' ||
				biography.length > 512 ||
				biography.length === 0
			) {
				return res.status(400).json({
					error:
						'Biography needs to be a string with a maximum of 512 characters and cannot be empty',
				});
			}

			if (
				!Array.isArray(parsedInterests) ||
				!parsedInterests.every(
					(i) => typeof i === 'string' && i.length <= 20 && i.length > 1
				)
			) {
				return res.status(400).json({
					error: 'Invalid interests format.',
				});
			}

			if (parsedInterests.length === 0) {
				return res.status(400).json({ error: 'Interests cannot be empty' });
			}

			if (parsedInterests.length > 5) {
				return res.status(400).json({ error: 'Maximum 5 interests allowed' });
			}

			const birth = new Date(birthdate);
			const today = new Date();
			const age = today.getFullYear() - birth.getFullYear();
			const m = today.getMonth() - birth.getMonth();
			const isBirthdayPassed =
				m > 0 || (m === 0 && today.getDate() >= birth.getDate());
			const actualAge = isBirthdayPassed ? age : age - 1;
			if (!birthdate || isNaN(Date.parse(birthdate))) {
				return res.status(400).json({ error: 'Invalid birthdate' });
			}
			if (actualAge < 18) {
				return res
					.status(400)
					.json({ error: 'You must be at least 18 years old' });
			}

			const pictures = req.files as Express.Multer.File[];
			if (pictures.length > 5) {
				return res.status(400).json({ error: 'Maximum 5 pictures allowed' });
			}

			if (pictures.length === 0) {
				return res
					.status(400)
					.json({ error: 'At least one picture for profile is required' });
			}

			for (const picture of pictures) {
				if (
					!['image/jpeg', 'image/png', 'image/webp'].includes(picture.mimetype)
				) {
					return res.status(400).json({
						error: 'Only JPEG, PNG, and WEBP image formats are allowed.',
					});
				}
			}

			for (const picture of pictures) {
				if (picture.size > 5 * 1024 * 1024) {
					return res.status(400).json({
						error: 'Each picture must be less than 5 MB.',
					});
				}
			}

			for (let i = 0; i < pictures.length; i++) {
				await userService.addUserPicture(
					user!.id,
					pictures[i].buffer,
					pictures[i].mimetype,
					i === 0
				);
			}

			await userService.completeUserProfile(user!.id, {
				gender,
				sexual_preference: parsedPreferences,
				biography,
				interests: parsedInterests,
				birthdate: new Date(birthdate),
			});

			res.status(200).json({ message: 'Profile completed successfully' });
		} catch (error: any) {
			logger.error(`Error completing profile: ${error.message}`);
			res.status(500).json({ error: 'Internal server error' });
		}
	}
);

router.put(
	'/update-basic-info',
	authMiddleware.authenticateToken,
	async (req: Request, res: Response) => {
		try {
			const user = await authMiddleware.getAuthenticatedUser(req);
			logger.info(`PUT /users/update-basic-info ${user!.username}`);

			const { first_name, last_name, email } = req.body;

			// Validate input
			if (first_name && (first_name.length < 2 || first_name.length > 50)) {
				return res.status(400).json({
					error: 'First name must be between 2 and 50 characters',
				});
			}

			if (last_name && (last_name.length < 2 || last_name.length > 50)) {
				return res.status(400).json({
					error: 'Last name must be between 2 and 50 characters',
				});
			}

			if (email && email.length <= 100 && isValidEmail(email)) {
				// Check if email is already taken by another user

				const existingUser = await userService.getUserByEmail(email);
				if (existingUser && existingUser.id !== user!.id) {
					return res.status(400).json({ error: 'Email is already in use' });
				}
			} else {
				if (!isValidEmail(email)) {
					return res.status(400).json({ error: 'Invalid email format' });
				}
			}

			await userService.updateUserBasicInfo(user!.id, {
				first_name,
				last_name,
				email,
			});

			res
				.status(200)
				.json({ message: 'Basic information updated successfully' });
		} catch (error: any) {
			logger.error(`Error updating basic info: ${error.message}`);
			res.status(500).json({ error: 'Internal server error' });
		}
	}
);

router.put(
	'/update-profile-info',
	authMiddleware.authenticateToken,
	async (req: Request, res: Response) => {
		try {
			const user = await authMiddleware.getAuthenticatedUser(req);
			logger.info(`PUT /users/update-profile-info ${user!.username}`);

			const { gender, sexual_preference, biography, interests } = req.body;

			// Validate gender
			if (gender && !GENDERS.includes(gender)) {
				return res.status(400).json({ error: 'Invalid gender' });
			}

			// Validate sexual preferences
			if (sexual_preference) {
				if (
					!Array.isArray(sexual_preference) ||
					!sexual_preference.every((g) => GENDERS.includes(g)) ||
					sexual_preference.length === 0
				) {
					return res.status(400).json({ error: 'Invalid sexual preferences' });
				}
			}

			// Validate biography
			if (
				biography &&
				(typeof biography !== 'string' ||
					biography.length > 512 ||
					biography.length === 0)
			) {
				return res.status(400).json({
					error:
						'Biography must be a string with a maximum of 512 characters and cannot be empty',
				});
			}

			// Validate interests
			if (interests) {
				if (
					!Array.isArray(interests) ||
					!interests.every(
						(i) => typeof i === 'string' && i.length <= 20 && i.length > 1
					) ||
					interests.length === 0 ||
					interests.length > 5
				) {
					return res.status(400).json({
						error:
							'Interests must be an array of 1-5 strings, each 2-20 characters long',
					});
				}
			}

			await userService.updateUserProfileInfo(user!.id, {
				gender,
				sexual_preference,
				biography,
				interests,
			});

			res
				.status(200)
				.json({ message: 'Profile information updated successfully' });
		} catch (error: any) {
			logger.error(`Error updating profile info: ${error.message}`);
			res.status(500).json({ error: 'Internal server error' });
		}
	}
);

/* Picture */
router.get('/:id/profile-picture', async (req, res) => {
	const id = parseInt(req.params.id, 10);

	logger.info(`GET /users/${id}/profile-picture`);

	if (isNaN(id)) return res.status(400).json({ error: 'Invalid user ID' });

	try {
		const user = await userService.getUserById(id);
		if (!user) return res.status(404).json({ error: 'User not found' });

		const pictureCount = await userService.countUserPictures(user.id);
		if (pictureCount === 0) {
			return res.status(404).json({ error: 'No profile picture found' });
		}

		const pictures: UserPicture | null =
			await userService.getUserProfilePicture(user.id);
		if (!pictures) {
			return res.status(404).json({ error: 'Profile picture not found' });
		}
		res.set('Content-Type', pictures.content_type);
		return res.send(pictures.data);
	} catch (error: any) {
		logger.error(`Error fetching user profile picture: ${error.message}`);
		return res.status(500).json({ error: 'Internal server error' });
	}
});

router.get(
	'/:id/picture-ids',
	authMiddleware.authenticateToken,
	async (req, res) => {
		const id = parseInt(req.params.id, 10);

		if (isNaN(id)) {
			return res.status(400).json({ error: 'Invalid user ID' });
		}

		logger.info(`GET /users/${id}/picture-ids`);

		try {
			const user = await userService.getUserById(id);
			if (!user) {
				return res.status(404).json({ error: 'User not found' });
			}

			const pictureIds = await userService.getUsersPicturesId(user.id);
			const idsOnly = pictureIds.map((pic) => pic.id);

			return res.status(200).json({ pictureIds: idsOnly });
		} catch (error: any) {
			logger.error(`Error fetching picture IDs: ${error.message}`);
			return res.status(500).json({ error: 'Internal server error' });
		}
	}
);

router.get('/pictures/:pictureId', async (req, res) => {
	const pictureId = parseInt(req.params.pictureId, 10);

	if (isNaN(pictureId)) {
		return res.status(400).json({ error: 'Invalid picture ID' });
	}

	logger.info(`GET /users/pictures/${pictureId}`);

	try {
		const picture = await userService.getPictureById(pictureId);
		if (!picture) {
			return res.status(404).json({ error: 'Picture not found' });
		}

		res.set('Content-Type', picture.content_type);
		return res.send(picture.data);
	} catch (error: any) {
		logger.error(`Error fetching picture by ID: ${error.message}`);
		return res.status(500).json({ error: 'Internal server error' });
	}
});

router.put(
	'/update-location',
	authMiddleware.authenticateToken,
	async (req, res) => {
		const user = await authMiddleware.getAuthenticatedUser(req);
		logger.info(`PUT /update-location ${user!.username}`);

		const { lat, lon, city, country } = req.body;

		if (typeof lat !== 'number' || typeof lon !== 'number') {
			return res.status(400).json({ error: 'Invalid latitude or longitude' });
		}

		if (typeof city !== 'string' || typeof country !== 'string') {
			return res.status(400).json({ error: 'Invalid city or country' });
		}

		try {
			await userService.updateUserLocation(user!.id, {
				country,
				city,
				longitude: lon,
				latitude: lat,
			});
			return res.status(200).json({ message: 'Location updated successfully' });
		} catch (error: any) {
			logger.error(`Error updating user location: ${error.message}`);
			return res.status(500).json({ error: 'Internal server error' });
		}
	}
);

router.put(
	'/update-password',
	authMiddleware.authenticateToken,
	async (req, res) => {
		const user = await authMiddleware.getAuthenticatedUser(req);

		logger.info(`PUT /users/update-password ${user!.username}`);

		const { newPassword } = req.body;

		if (isStrongPassword(newPassword) === false) {
			return res.status(400).json({
				error:
					'Password must be at least 8 characters long, contain uppercase and lowercase letters, numbers, and special characters',
			});
		}

		const hashedPassword = bcrypt.hashSync(newPassword, 10);

		try {
			await userService.updateUserPassword(user!.id, hashedPassword);

			return res.status(200).json({ message: 'Password updated successfully' });
		} catch (error: any) {
			logger.error(`Error updating password: ${error.message}`);
			return res.status(500).json({ error: 'Internal server error' });
		}
	}
);

/* Picture Management */
router.post(
	'/upload-picture',
	authMiddleware.authenticateToken,
	upload.single('picture'),
	async (req: Request, res: Response) => {
		try {
			const user = await authMiddleware.getAuthenticatedUser(req);
			logger.info(`POST /upload-picture ${user!.username}`);

			const picture = req.file;
			if (!picture) {
				return res.status(400).json({ error: 'No picture provided' });
			}

			// Check if user already has 5 pictures
			const pictureCount = await userService.countUserPictures(user!.id);
			if (pictureCount >= 5) {
				return res.status(400).json({ error: 'Maximum 5 pictures allowed' });
			}

			// Validate file type
			if (
				!['image/jpeg', 'image/png', 'image/webp'].includes(picture.mimetype)
			) {
				return res.status(400).json({
					error: 'Only JPEG, PNG, and WEBP image formats are allowed.',
				});
			}

			// Validate file size (5MB max)
			if (picture.size > 5 * 1024 * 1024) {
				return res.status(400).json({
					error: 'Picture must be less than 5 MB.',
				});
			}

			const result = await userService.addSingleUserPicture(
				user!.id,
				picture.buffer,
				picture.mimetype
			);

			res.status(201).json({
				message: 'Picture uploaded successfully',
				pictureId: result.id,
			});
		} catch (error: any) {
			logger.error(`Error uploading picture: ${error.message}`);
			res.status(500).json({ error: 'Internal server error' });
		}
	}
);

router.delete(
	'/pictures/:pictureId',
	authMiddleware.authenticateToken,
	async (req: Request, res: Response) => {
		try {
			const user = await authMiddleware.getAuthenticatedUser(req);
			const pictureId = parseInt(req.params.pictureId, 10);

			if (isNaN(pictureId)) {
				return res.status(400).json({ error: 'Invalid picture ID' });
			}

			logger.info(`DELETE /pictures/${pictureId} ${user!.username}`);

			// Check if picture exists and belongs to user
			const picture = await userService.getPictureById(pictureId);
			if (!picture) {
				return res.status(404).json({ error: 'Picture not found' });
			}

			if (picture.user_id !== user!.id) {
				return res.status(403).json({ error: 'Unauthorized' });
			}

			const deleted = await userService.deletePicture(pictureId, user!.id);
			if (!deleted) {
				return res.status(404).json({ error: 'Picture not found' });
			}

			res.status(200).json({ message: 'Picture deleted successfully' });
		} catch (error: any) {
			logger.error(`Error deleting picture: ${error.message}`);
			res.status(500).json({ error: 'Internal server error' });
		}
	}
);

router.put(
	'/pictures/:pictureId/set-profile',
	authMiddleware.authenticateToken,
	async (req: Request, res: Response) => {
		try {
			const user = await authMiddleware.getAuthenticatedUser(req);
			const pictureId = parseInt(req.params.pictureId, 10);

			if (isNaN(pictureId)) {
				return res.status(400).json({ error: 'Invalid picture ID' });
			}

			logger.info(`PUT /pictures/${pictureId}/set-profile ${user!.username}`);

			// Check if picture exists and belongs to user
			const picture = await userService.getPictureById(pictureId);
			if (!picture) {
				return res.status(404).json({ error: 'Picture not found' });
			}

			if (picture.user_id !== user!.id) {
				return res.status(403).json({ error: 'Unauthorized' });
			}

			const updated = await userService.setProfilePicture(pictureId, user!.id);
			if (!updated) {
				return res.status(404).json({ error: 'Picture not found' });
			}

			res.status(200).json({ message: 'Profile picture set successfully' });
		} catch (error: any) {
			logger.error(`Error setting profile picture: ${error.message}`);
			res.status(500).json({ error: 'Internal server error' });
		}
	}
);

router.get(
	'/last-connection/:userId',
	authMiddleware.authenticateToken,
	async (req, res) => {
		const userId = parseInt(req.params.userId, 10);

		if (isNaN(userId)) {
			return res.status(400).json({ error: 'Invalid user ID' });
		}

		logger.info(`GET /last-connection/${userId}`);

		try {
			const lastConnection = await userService.getLastConnectionInMinutes(
				userId
			);
			return res.status(200).json({ last_connection: lastConnection });
		} catch (error: any) {
			logger.error(`Error fetching last connection: ${error.message}`);
			return res.status(500).json({ error: 'Internal server error' });
		}
	}
);

export default router;
