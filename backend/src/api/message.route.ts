import express, { Request, Response } from 'express';
import { AuthMiddleware } from '../middleware/auth/auth.middleware';
import logger from '../utils/logger';
import { MessageService } from '../service/message.service';
const router = express.Router();

const messageService = new MessageService();
const authMiddleware = new AuthMiddleware();

router.get(
	'/:recipientId',
	authMiddleware.authenticateToken,
	async (req: Request, res: Response) => {
		try {
			const user = await authMiddleware.getAuthenticatedUser(req);
			const recipientId = parseInt(req.params.recipientId, 10);
			if (isNaN(recipientId)) {
				return res.status(400).json({ error: 'Invalid recipient ID' });
			}

			const messages = await messageService.getMessagesBetweenUsers(
				user!.id,
				recipientId
			);
			return res.status(200).json(messages);
		} catch (error) {
			logger.error('Error fetching messages:', error);
			return res.status(500).json({ error: 'Internal server error' });
		}
	}
);

export default router;
