import express, { Request, Response } from 'express';
import { AuthMiddleware } from '../middleware/auth/auth.middleware';
import { ViewService } from '../service/view.service';
import logger from '../utils/logger';
const router = express.Router();

const viewService = new ViewService();
const authMiddleware = new AuthMiddleware();

router.get(
	'/:userId',
	authMiddleware.authenticateToken,
	async (req: Request, res: Response) => {
		try {
			logger.info('GET /view');

			const userId = parseInt(req.params.userId, 10);
			const users = await viewService.getViews(userId);

			return res.status(200).json(users);
		} catch (error) {
			return res.status(500).json({ error: 'Internal server error' });
		}
	}
);

router.post(
	'/:userId',
	authMiddleware.authenticateToken,
	async (req: Request, res: Response) => {
		try {
			const user = await authMiddleware.getAuthenticatedUser(req);
			const userId = parseInt(req.params.userId, 10);

			if (!userId || isNaN(userId)) {
				return res.status(400).json({ error: 'Invalid user ID' });
			}

			if (user!.id === userId) {
				return res.status(204);
			}

			logger.info(
				`POST /views/${userId} - User ID: ${user!.id} viewed User ID: ${userId}`
			);

			await viewService.createView(user!, userId);
			return res.status(201).json({ message: 'View recorded successfully' });
		} catch (error) {
			return res.status(500).json({ error: 'Internal server error' });
		}
	}
);

export default router;
