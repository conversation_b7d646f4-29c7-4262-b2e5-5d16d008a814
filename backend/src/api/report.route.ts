import express, { Request, Response } from 'express';
import { AuthMiddleware } from '../middleware/auth/auth.middleware';
import { ReportService } from '../service/report.service';
import logger from '../utils/logger';
const router = express.Router();

const reportService = new ReportService();
const authMiddleware = new AuthMiddleware();

/* Is user reported*/
router.get(
	'/:userId',
	authMiddleware.authenticateToken,
	async (req: Request, res: Response) => {
		try {
			const userId = parseInt(req.params.userId, 10);
			if (isNaN(userId)) {
				return res.status(400).json({ error: 'Invalid user ID' });
			}

			const user = await authMiddleware.getAuthenticatedUser(req);

			logger.info(`GET /report/${userId}`);
			const isReported = await reportService.isReported(user!.id, userId);
			return res.status(200).json(isReported);
		} catch (error) {
			logger.error('Error fetching blocked users:', error);
			return res.status(500).json({ error: 'Internal server error' });
		}
	}
);

/* Toggle report user */
router.post(
	'/:userId',
	authMiddleware.authenticateToken,
	async (req: Request, res: Response) => {
		try {
			const user = await authMiddleware.getAuthenticatedUser(req);
			const userId = parseInt(req.params.userId, 10);

			if (!userId || isNaN(userId)) {
				return res.status(400).json({ error: 'Invalid user ID' });
			}

			if (user!.id === userId) {
				return res.status(204).send();
			}

			logger.info(
				`POST /report/${userId} - User ID: ${
					user!.id
				} toggled report for User ID: ${userId}`
			);

			await reportService.toggleReport(user!.id, userId);
			return res
				.status(201)
				.json({ message: 'Block status toggled successfully' });
		} catch (error) {
			logger.error('Error toggling block status:', error);
			return res.status(500).json({ error: 'Internal server error' });
		}
	}
);

export default router;
