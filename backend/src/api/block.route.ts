import express, { Request, Response } from 'express';
import { AuthMiddleware } from '../middleware/auth/auth.middleware';
import { BlockService } from '../service/block.service';
import logger from '../utils/logger';
const router = express.Router();

const blockService = new BlockService();
const authMiddleware = new AuthMiddleware();

/* Is user blocked*/
router.get(
	'/:userId',
	authMiddleware.authenticateToken,
	async (req: Request, res: Response) => {
		try {
			const userId = parseInt(req.params.userId, 10);
			if (isNaN(userId)) {
				return res.status(400).json({ error: 'Invalid user ID' });
			}

			const user = await authMiddleware.getAuthenticatedUser(req);

			logger.info(`GET /block/${userId}`);
			const isBlocked = await blockService.isBlocked(user!.id, userId);
			return res.status(200).json(isBlocked);
		} catch (error) {
			logger.error('Error fetching blocked users:', error);
			return res.status(500).json({ error: 'Internal server error' });
		}
	}
);

/* Toggle block user */
router.post(
	'/:userId',
	authMiddleware.authenticateToken,
	async (req: Request, res: Response) => {
		try {
			const user = await authMiddleware.getAuthenticatedUser(req);
			const userId = parseInt(req.params.userId, 10);

			if (!userId || isNaN(userId)) {
				return res.status(400).json({ error: 'Invalid user ID' });
			}

			if (user!.id === userId) {
				return res.status(204).send();
			}

			logger.info(
				`POST /block/${userId} - User ID: ${
					user!.id
				} toggled block for User ID: ${userId}`
			);

			await blockService.toggleBlock(user!.id, userId);
			return res
				.status(201)
				.json({ message: 'Block status toggled successfully' });
		} catch (error) {
			logger.error('Error toggling block status:', error);
			return res.status(500).json({ error: 'Internal server error' });
		}
	}
);

export default router;
