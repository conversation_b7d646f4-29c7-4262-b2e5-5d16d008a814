import express from 'express';
const router = express.Router();
import logger from '../../utils/logger';
import bcrypt from 'bcryptjs';
import {
	createUser,
	getUserByEmail,
	getUserByUsername,
} from '../../service/user.service';
import { generateToken } from '../../auth/auth.utils';
import { authenticateToken } from '../../auth/auth.middleware';
import { isStrongPassword, isValidEmail } from '../../utils/validation';

router.post('/login', async (req, res) => {
	const { username, password } = req.body || {};
	logger.info(`POST /login ${username ? `${username}` : ''}`);

	if (!username || !password) {
		return res
			.status(400)
			.json({ error: 'Username and password are required' });
	}

	try {
		// Fetch user by username
		const user = await getUserByUsername(username);
		if (!user) {
			return res.status(404).json({ error: 'User not found' });
		}

		// Compare password with stored hash
		const isMatch = await bcrypt.compare(password, user.password);
		if (!isMatch) {
			return res.status(401).json({ error: 'Invalid credentials' });
		}

		// Generate JWT token
		const token = generateToken({
			id: user.id,
			username: user.username,
			email: user.email,
		});
		logger.info(`${username} logged in successfully`);
		return res.status(200).json({
			token,
			user: { id: user.id, username: user.username, email: user.email },
		});
	} catch (error: any) {
		logger.error(`Error fetching user: ${error.message}`);
		return res.status(500).json({ error: 'Internal server error' });
	}
});

router.post('/register', async (req, res) => {
	const { first_name, last_name, username, email, password } = req.body || {};
	logger.info(`POST /register ${username ? `${username}` : ''}`);
	if (!first_name || !last_name || !username || !email || !password) {
		return res.status(400).json({ error: 'All fields are required' });
	}

	if (await getUserByUsername(username)) {
		return res.status(400).json({ error: 'Username is already taken' });
	}

	if (await getUserByEmail(email)) {
		return res.status(400).json({ error: 'Email is already in use' });
	}

	if (isValidEmail(email) === false) {
		return res.status(400).json({ error: 'Invalid email format' });
	}

	if (isStrongPassword(password) === false) {
		return res.status(400).json({
			error:
				'Password must be at least 8 characters long, contain uppercase and lowercase letters, numbers, and special characters',
		});
	}

	try {
		await createUser({
			first_name,
			last_name,
			username,
			email,
			password: bcrypt.hashSync(password, 10),
		});
	} catch (error: any) {
		logger.error(`Error creating user: ${error.message}`);
		return res.status(500).json({ error: 'Internal server error' });
	}
	logger.info(`${username} registered successfully`);
	return res.status(200).json({ message: 'Registration successful' });
});

router.get('/me', authenticateToken, (req, res) => {
	logger.info('GET /me');
	const { id, username, email } = (req as any).user || {};
	return res.status(200).json({ id, username, email });
});

export default router;
