import express, { Request, Response } from 'express';
import { AuthMiddleware } from '../middleware/auth/auth.middleware';
import { LikeService } from '../service/like.service';
import logger from '../utils/logger';
import { sanitizeUsers } from '../sanitize/user.sanitize';
import { UserService } from '../service/user.service';
const router = express.Router();

const likeService = new LikeService();
const userService = new UserService();
const authMiddleware = new AuthMiddleware();

router.get(
	'/matchs',
	authMiddleware.authenticateToken,
	async (req: Request, res: Response) => {
		try {
			logger.info('GET /like/matchs');

			const user = await authMiddleware.getAuthenticatedUser(req);
			const users = await likeService.getMatchs(user!.id);

			return res.status(200).json(sanitizeUsers(users));
		} catch (error) {
			return res.status(500).json({ error: 'Internal server error' });
		}
	}
);

router.get(
	'/:userId',
	authMiddleware.authenticateToken,
	async (req: Request, res: Response) => {
		try {
			const userId = parseInt(req.params.userId, 10);

			logger.info(`GET /like/${userId}`);
			const users = await likeService.getLikes(userId);

			return res.status(200).json(sanitizeUsers(users));
		} catch (error) {
			return res.status(500).json({ error: 'Internal server error' });
		}
	}
);

router.get(
	'/following/:userId',
	authMiddleware.authenticateToken,
	async (req: Request, res: Response) => {
		try {
			const userId = parseInt(req.params.userId, 10);

			logger.info(`GET /like/following/${userId}`);
			const users = await likeService.getUsersILiked(userId);

			return res.status(200).json(sanitizeUsers(users));
		} catch (error) {
			return res.status(500).json({ error: 'Internal server error' });
		}
	}
);

router.get(
	'/mutual/:userId',
	authMiddleware.authenticateToken,
	async (req: Request, res: Response) => {
		try {
			const user = await authMiddleware.getAuthenticatedUser(req);
			const userId = parseInt(req.params.userId, 10);
			if (isNaN(userId)) {
				return res.status(400).json({ error: 'Invalid user ID' });
			}

			const isMutual = await likeService.isMutualLike(user!.id, userId);
			return res.status(200).json({ mutual: isMutual });
		} catch (error) {
			return res.status(500).json({ error: 'Internal server error' });
		}
	}
);

router.get(
	'/is-liked/:userId',
	authMiddleware.authenticateToken,
	async (req: Request, res: Response) => {
		try {
			const user = await authMiddleware.getAuthenticatedUser(req);
			const userId = parseInt(req.params.userId, 10);
			if (isNaN(userId)) {
				return res.status(400).json({ error: 'Invalid user ID' });
			}
			const liked = await likeService.isLiked(user!.id, userId);
			return res.status(200).json({ liked });
		} catch (error) {
			return res.status(500).json({ error: 'Internal server error' });
		}
	}
);

router.post(
	'/:userId',
	authMiddleware.authenticateToken,
	async (req: Request, res: Response) => {
		try {
			const user = await authMiddleware.getAuthenticatedUser(req);
			const userId = parseInt(req.params.userId, 10);
			if (isNaN(userId)) {
				return res.status(400).json({ error: 'Invalid user ID' });
			}

			const toUser = await userService.getUserById(userId);
			if (!toUser) {
				return res.status(404).json({ error: 'User not found' });
			}

			if (user!.id === userId) {
				return res
					.status(400)
					.json({ error: 'You cannot like yourself sorry...' });
			}

			await likeService.toggleLike(user!, toUser!);
			return res.status(201).json({ message: 'Like toggled successfully' });
		} catch (error) {
			return res.status(500).json({ error: 'Internal server error' });
		}
	}
);

export default router;
