import express from 'express';
const router = express.Router();
import logger from '../utils/logger';
import bcrypt from 'bcryptjs';
import { UserService } from '../service/user.service';
import {
	generateToken,
	geolocateIp,
	getClientIp,
} from '../middleware/auth/auth.utils';
import { isStrongPassword, isValidEmail } from '../utils/validation';
import { AuthMiddleware } from '../middleware/auth/auth.middleware';

const userService = new UserService();
const authMiddleware = new AuthMiddleware();

/**
 * @swagger
 * /api/auth/login:
 *   post:
 *     summary: Logs in a user
 *     tags:
 *       - Auth
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               username:
 *                 type: string
 *               password:
 *                 type: string
 *     responses:
 *       200:
 *         description: Login successful
 *       400:
 *         description: Bad request
 *       500:
 *         description: Internal server error
 */
router.post('/login', async (req, res) => {
	const { username, password, lat, lon, city, country } = req.body || {};
	const ip = getClientIp(req);

	logger.info(`POST /login ${username ? `${username}` : ''} from IP ${ip}`);

	if (!username || !password) {
		return res
			.status(400)
			.json({ error: 'Username and password are required' });
	}

	try {
		const user = await userService.getUserByUsername(username);
		if (!user) {
			return res
				.status(400)
				.json({ error: 'Username or password is incorrect' });
		}

		if (!(await userService.isEmailVerified(user.email))) {
			return res.status(400).json({
				error:
					'Email not verified. Please check your email for verification link.',
			});
		}

		const isMatch = await bcrypt.compare(password, user.password);
		if (!isMatch) {
			return res
				.status(400)
				.json({ error: 'Username or password is incorrect' });
		}

		const token = generateToken({
			id: user.id,
			username: user.username,
			email: user.email,
		});

		let userLocation = null;

		if (lat && lon && city && country) {
			userLocation = {
				latitude: parseFloat(lat),
				longitude: parseFloat(lon),
				city,
				country,
			};
		} else {
			const geo = await geolocateIp(ip);
			if (geo && geo.lat && geo.lon && geo.city && geo.country) {
				userLocation = {
					latitude: geo.lat,
					longitude: geo.lon,
					city: geo.city,
					country: geo.country,
				};
			}
		}

		// Sauvegarde localisation si dispo
		if (userLocation) {
			await userService.updateUserLocation(user.id, userLocation);
			logger.debug(
				`Updated location for user ${user.username} (${user.id}) to ${userLocation.city}, ${userLocation.country} (${userLocation.longitude}, ${userLocation.latitude})`
			);
		}

		logger.info(`${username} logged in successfully`);
		return res.status(200).json({
			token,
			user: { id: user.id, username: user.username, email: user.email },
		});
	} catch (error: any) {
		logger.error(`Error fetching user: ${error.message}`);
		return res.status(500).json({ error: 'Internal server error' });
	}
});

/**
 * @swagger
 * /api/auth/register:
 *   post:
 *     summary: Registers a new user
 *     tags:
 *       - Auth
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               first_name:
 *                 type: string
 *               last_name:
 *                 type: string
 *               username:
 *                 type: string
 *               email:
 *                 type: string
 *               password:
 *                 type: string
 *     responses:
 *       200:
 *         description: Registration successful
 *       400:
 *         description: Bad request
 *       500:
 *         description: Internal server error
 */
router.post('/register', async (req, res) => {
	const { first_name, last_name, username, email, password } = req.body || {};
	logger.info(`POST /register ${username ? `${username}` : ''}`);
	if (!first_name || !last_name || !username || !email || !password) {
		return res.status(400).json({ error: 'All fields are required' });
	}

	if (first_name > 50 || first_name.length < 5) {
		return res.status(400).json({
			error:
				'FirstName must be less than 50 characters and at least 5 characters long',
		});
	}

	if (last_name > 50 || last_name.length < 5) {
		return res.status(400).json({
			error:
				'LastName must be less than 50 characters and at least 5 characters long',
		});
	}

	if (username > 50 || username.length < 5) {
		return res.status(400).json({
			error:
				'Username must be less than 50 characters and at least 5 characters long',
		});
	}

	if (await userService.getUserByUsername(username)) {
		return res.status(400).json({ error: 'Username is already taken' });
	}

	if (await userService.getUserByEmail(email)) {
		return res.status(400).json({ error: 'Email is already in use' });
	}

	if (isValidEmail(email) === false) {
		return res.status(400).json({ error: 'Invalid email format' });
	}

	if (email.length > 100) {
		return res
			.status(400)
			.json({ error: 'Email must be less than 100 characters' });
	}

	if (isStrongPassword(password) === false) {
		return res.status(400).json({
			error:
				'Password must be at least 8 characters long, contain uppercase and lowercase letters, numbers, and special characters',
		});
	}

	try {
		await userService.createUser({
			first_name,
			last_name,
			username,
			email,
			password: bcrypt.hashSync(password, 10),
		});
	} catch (error: any) {
		logger.error(`Error creating user: ${error.message}`);
		return res.status(500).json({ error: 'Internal server error' });
	}
	logger.info(`${username} registered successfully`);
	return res.status(200).json({ message: 'Registration successful' });
});

/**
 * @swagger
 * /api/auth/verify-email:
 *   get:
 *     summary: Verifies a user's email address
 *     tags:
 *       - Auth
 *     parameters:
 *       - in: query
 *         name: token
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Email verified successfully
 *       400:
 *         description: Bad request
 *       500:
 *         description: Internal server error
 */
router.get('/verify-email', async (req, res) => {
	const { token } = req.query;
	logger.info(`GET /verify-email ${token ?? ''}`);

	if (!token || typeof token !== 'string') {
		return res.status(400).send('<h2>Missing email verification token.</h2>');
	}

	try {
		if (!(await userService.isEmailVerificationTokenValid(token))) {
			return res.status(400).send('<h2>Invalid or expired token.</h2>');
		}

		await userService.updateEmailVerificationToken(token);

		return res.send(`
			<!DOCTYPE html>
			<html lang="en">
			<head>
				<meta charset="UTF-8" />
				<meta name="viewport" content="width=device-width, initial-scale=1.0" />
				<title>Email Verified</title>
				<style>
					body {
						font-family: sans-serif;
						display: flex;
						justify-content: center;
						align-items: center;
						height: 100vh;
						flex-direction: column;
						text-align: center;
					}
				</style>
				<script>
					setTimeout(function () {
						window.location.href = "${process.env.FRONTEND_URL}/login";
					}, 3000);
				</script>
			</head>
			<body>
				<h1>Your email has been successfully verified!</h1>
				<p>You will be redirected to the login page shortly...</p>
			</body>
			</html>
		`);
	} catch (error: any) {
		logger.error(`Error verifying email: ${error.message}`);
		return res
			.status(500)
			.send(
				'<h2>Something went wrong during verification. Please try again later.</h2>'
			);
	}
});

/**
 * @swagger
 * /api/auth/me:
 *   get:
 *     summary: Gets the currently logged in user
 *     tags:
 *       - Auth
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Success
 *       401:
 *         description: Unauthorized
 */
router.get('/me', authMiddleware.authenticateToken, (req, res) => {
	logger.info('GET /me');
	const { id, username, email } = (req as any).user || {};
	userService.updateLastConnection(id);
	return res.status(200).json({ id, username, email });
});

/**
 * @swagger
 * /api/auth/forgot-password:
 *   post:
 *     summary: Initiates password reset process
 *     tags:
 *       - Auth
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               email:
 *                 type: string
 *     responses:
 *       200:
 *         description: Password reset email sent
 *       400:
 *         description: Bad request
 *       500:
 *         description: Internal server error
 */
router.post('/forgot-password', async (req, res) => {
	const { email } = req.body || {};

	logger.info(`POST /forgot-password ${email ? `${email}` : ''}`);

	if (!email) {
		return res.status(400).json({ error: 'Email is required' });
	}

	if (isValidEmail(email) === false) {
		return res.status(400).json({ error: 'Invalid email format' });
	}

	try {
		userService.forgotPassword(email);
		return res
			.status(200)
			.json({ message: 'Password reset email sent successfully' });
	} catch (error: any) {
		logger.error(`Error sending password reset email: ${error.message}`);
		return res.status(500).json({ error: 'Internal server error' });
	}
});

router.post('/reset-password', async (req, res) => {
	const { token, newPassword } = req.body || {};

	logger.info(`POST /reset-password ${token ? `${token}` : ''}`);

	if (!token || !newPassword) {
		return res
			.status(400)
			.json({ error: 'Token and new password are required' });
	}

	if (isStrongPassword(newPassword) === false) {
		return res.status(400).json({
			error:
				'Password must be at least 8 characters long, contain uppercase and lowercase letters, numbers, and special characters',
		});
	}

	try {
		const user = await userService.getUserByPasswordResetToken(token);
		if (!user) {
			return res.status(400).json({ error: 'Invalid or expired token' });
		}

		const hashedPassword = bcrypt.hashSync(newPassword, 10);
		await userService.updateUserPassword(user.id, hashedPassword);
		await userService.updateUserPasswordResetToken(user.id, null);

		return res.status(200).json({ message: 'Password changed successfully' });
	} catch (error: any) {
		logger.error(`Error resetting password: ${error.message}`);
		return res.status(500).json({ error: 'Internal server error' });
	}
});

export default router;
