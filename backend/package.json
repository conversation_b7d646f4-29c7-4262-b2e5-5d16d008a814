{"name": "backend", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"build": "npx tsc", "start": "node dist/server.js", "dev": "nodemon --exec ts-node /app/src/server.ts", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@faker-js/faker": "^9.9.0", "axios": "^1.11.0", "bcryptjs": "^3.0.2", "chalk": "^4.1.2", "cors": "^2.8.5", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "multer": "^2.0.2", "nodemailer": "^7.0.5", "pg": "^8.16.3", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "ts-node": "^10.9.2", "winston": "^3.17.0", "ws": "^8.18.3"}, "devDependencies": {"@types/axios": "^0.14.4", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jsonwebtoken": "^9.0.10", "@types/multer": "^2.0.0", "@types/node": "^24.1.0", "@types/nodemailer": "^6.4.17", "@types/pg": "^8.15.4", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.8", "@types/ws": "^8.18.1", "nodemon": "^3.1.10", "typescript": "^5.8.3"}}