{"name": "backend", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"build": "npx tsc", "start": "node dist/server.js", "dev": "nodemon --exec ts-node /app/src/server.ts", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"axios": "^1.10.0", "bcryptjs": "^3.0.2", "chalk": "^4.1.2", "cors": "^2.8.5", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "nodemailer": "^7.0.5", "pg": "^8.16.3", "ts-node": "^10.9.2", "winston": "^3.17.0"}, "devDependencies": {"@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^24.1.0", "@types/nodemailer": "^6.4.17", "@types/pg": "^8.15.4", "nodemon": "^3.1.10", "typescript": "^5.8.3"}}