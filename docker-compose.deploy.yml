services:
  angular:
    build:
      context: ./frontend
      dockerfile: Dockerfile.deploy
    container_name: matcha-angular
    ports:
      - '127.0.0.1:${NGINX_PORT:-80}:80'
    networks:
      - matcha-network
    restart: unless-stopped
    mem_limit: 64MB

  express:
    build:
      context: ./backend
      dockerfile: Dockerfile.deploy
    container_name: matcha-express
    environment:
      POSTGRES_HOST: ${POSTGRES_HOST}
      POSTGRES_DB: ${POSTGRES_DB}
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      JWT_SECRET: ${JWT_SECRET}
      JWT_EXPIRATION: ${JWT_EXPIRATION}
      SMTP_EMAIL_HOST: ${SMTP_EMAIL_HOST}
      SMTP_EMAIL_PORT: ${SMTP_EMAIL_PORT}
      SMTP_EMAIL_USER: ${SMTP_EMAIL_USER}
      SMTP_EMAIL_PASSWORD: ${SMTP_EMAIL_PASSWORD}
      USER_MVAN_PEE_PASSWORD: ${USER_MVAN_PEE_PASSWORD}
      USER_RPEREZ_T_PASSWORD: ${USER_RPEREZ_T_PASSWORD}
    expose:
      - '3000'
    depends_on:
      - postgres
    networks:
      - matcha-network
    restart: unless-stopped
    mem_limit: 128MB

  postgres:
    image: postgres:13-alpine
    container_name: matcha-postgres
    environment:
      POSTGRES_HOST: ${POSTGRES_HOST}
      POSTGRES_DB: ${POSTGRES_DB}
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    expose:
      - '5432'
    volumes:
      - matcha-data:/var/lib/postgresql/data
    networks:
      - matcha-network
    restart: unless-stopped
    mem_limit: 128MB

networks:
  matcha-network:
    driver: bridge

volumes:
  matcha-data:
    external: true
