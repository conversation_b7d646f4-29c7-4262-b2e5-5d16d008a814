name: Deploy to VPS

on:
  push:
    branches:
      - main
  workflow_dispatch:

jobs:
  deploy:
    runs-on: melvium.com
    environment: prod

    steps:
      - name: Checkout Code
        uses: actions/checkout@v3

      - name: Set up Docker
        uses: docker/setup-buildx-action@v2

      - name: Init deployement
        run: |
          docker volume create matcha-data || true

      - name: Build and runs containers
        env:
          NGINX_PORT: ${{ vars.NGINX_PORT }}
          POSTGRES_HOST: ${{ vars.POSTGRES_HOST }}
          POSTGRES_DB: ${{ vars.POSTGRES_DB }}
          POSTGRES_USER: ${{ vars.POSTGRES_USER }}
          POSTGRES_PASSWORD: ${{ vars.POSTGRES_PASSWORD }}
          JWT_SECRET: ${{ vars.JWT_SECRET }}
          JWT_EXPIRATION: ${{ vars.JWT_EXPIRATION }}
          SMTP_EMAIL_HOST: ${{ vars.SMTP_EMAIL_HOST }}
          SMTP_EMAIL_PORT: ${{ vars.SMTP_EMAIL_PORT }}
          SMTP_EMAIL_USER: ${{ vars.SMTP_EMAIL_USER }}
          SMTP_EMAIL_PASSWORD: ${{ vars.SMTP_EMAIL_PASSWORD }}
          USER_MVAN_PEE_PASSWORD: ${{ vars.USER_MVAN_PEE_PASSWORD }}
          USER_RPEREZ_T_PASSWORD: ${{ vars.USER_RPEREZ_T_PASSWORD }}
        run: |
          docker compose -f docker-compose.deploy.yml down -v
          docker compose -f docker-compose.deploy.yml build --no-cache
          docker compose -f docker-compose.deploy.yml up -d

      - name: Logs the containers
        run: |
          docker container ls -a
          docker stats --no-stream
